/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(self["webpackChunk_N_E"] = self["webpackChunk_N_E"] || []).push([["app/layout"],{

/***/ "(app-pages-browser)/./node_modules/next-themes/dist/index.mjs":
/*!*************************************************!*\
  !*** ./node_modules/next-themes/dist/index.mjs ***!
  \*************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

"use strict";
eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   ThemeProvider: () => (/* binding */ J),\n/* harmony export */   useTheme: () => (/* binding */ z)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* __next_internal_client_entry_do_not_use__ ThemeProvider,useTheme auto */ var _s = $RefreshSig$(), _s1 = $RefreshSig$(), _s2 = $RefreshSig$();\n\nvar M = (e, i, s, u, m, a, l, h)=>{\n    let d = document.documentElement, w = [\n        \"light\",\n        \"dark\"\n    ];\n    function p(n) {\n        (Array.isArray(e) ? e : [\n            e\n        ]).forEach((y)=>{\n            let k = y === \"class\", S = k && a ? m.map((f)=>a[f] || f) : m;\n            k ? (d.classList.remove(...S), d.classList.add(a && a[n] ? a[n] : n)) : d.setAttribute(y, n);\n        }), R(n);\n    }\n    function R(n) {\n        h && w.includes(n) && (d.style.colorScheme = n);\n    }\n    function c() {\n        return window.matchMedia(\"(prefers-color-scheme: dark)\").matches ? \"dark\" : \"light\";\n    }\n    if (u) p(u);\n    else try {\n        let n = localStorage.getItem(i) || s, y = l && n === \"system\" ? c() : n;\n        p(y);\n    } catch (n) {}\n};\n_c = M;\nvar b = [\n    \"light\",\n    \"dark\"\n], I = \"(prefers-color-scheme: dark)\", O = \"object\" == \"undefined\", x = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.createContext(void 0), U = {\n    setTheme: (e)=>{},\n    themes: []\n}, z = ()=>{\n    _s();\n    var e;\n    return (e = react__WEBPACK_IMPORTED_MODULE_0__.useContext(x)) != null ? e : U;\n}, J = (e)=>{\n    _s1();\n    return react__WEBPACK_IMPORTED_MODULE_0__.useContext(x) ? /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.createElement(react__WEBPACK_IMPORTED_MODULE_0__.Fragment, null, e.children) : /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.createElement(V, {\n        ...e\n    });\n}, N = [\n    \"light\",\n    \"dark\"\n], V = (param)=>{\n    let { forcedTheme: e, disableTransitionOnChange: i = !1, enableSystem: s = !0, enableColorScheme: u = !0, storageKey: m = \"theme\", themes: a = N, defaultTheme: l = s ? \"system\" : \"light\", attribute: h = \"data-theme\", value: d, children: w, nonce: p, scriptProps: R } = param;\n    _s2();\n    let [c, n] = react__WEBPACK_IMPORTED_MODULE_0__.useState({\n        \"V.useState\": ()=>H(m, l)\n    }[\"V.useState\"]), [T, y] = react__WEBPACK_IMPORTED_MODULE_0__.useState({\n        \"V.useState\": ()=>c === \"system\" ? E() : c\n    }[\"V.useState\"]), k = d ? Object.values(d) : a, S = react__WEBPACK_IMPORTED_MODULE_0__.useCallback({\n        \"V.useCallback[S]\": (o)=>{\n            let r = o;\n            if (!r) return;\n            o === \"system\" && s && (r = E());\n            let v = d ? d[r] : r, C = i ? W(p) : null, P = document.documentElement, L = {\n                \"V.useCallback[S].L\": (g)=>{\n                    g === \"class\" ? (P.classList.remove(...k), v && P.classList.add(v)) : g.startsWith(\"data-\") && (v ? P.setAttribute(g, v) : P.removeAttribute(g));\n                }\n            }[\"V.useCallback[S].L\"];\n            if (Array.isArray(h) ? h.forEach(L) : L(h), u) {\n                let g = b.includes(l) ? l : null, D = b.includes(r) ? r : g;\n                P.style.colorScheme = D;\n            }\n            C == null || C();\n        }\n    }[\"V.useCallback[S]\"], [\n        p\n    ]), f = react__WEBPACK_IMPORTED_MODULE_0__.useCallback({\n        \"V.useCallback[f]\": (o)=>{\n            let r = typeof o == \"function\" ? o(c) : o;\n            n(r);\n            try {\n                localStorage.setItem(m, r);\n            } catch (v) {}\n        }\n    }[\"V.useCallback[f]\"], [\n        c\n    ]), A = react__WEBPACK_IMPORTED_MODULE_0__.useCallback({\n        \"V.useCallback[A]\": (o)=>{\n            let r = E(o);\n            y(r), c === \"system\" && s && !e && S(\"system\");\n        }\n    }[\"V.useCallback[A]\"], [\n        c,\n        e\n    ]);\n    react__WEBPACK_IMPORTED_MODULE_0__.useEffect({\n        \"V.useEffect\": ()=>{\n            let o = window.matchMedia(I);\n            return o.addListener(A), A(o), ({\n                \"V.useEffect\": ()=>o.removeListener(A)\n            })[\"V.useEffect\"];\n        }\n    }[\"V.useEffect\"], [\n        A\n    ]), react__WEBPACK_IMPORTED_MODULE_0__.useEffect({\n        \"V.useEffect\": ()=>{\n            let o = {\n                \"V.useEffect.o\": (r)=>{\n                    r.key === m && (r.newValue ? n(r.newValue) : f(l));\n                }\n            }[\"V.useEffect.o\"];\n            return window.addEventListener(\"storage\", o), ({\n                \"V.useEffect\": ()=>window.removeEventListener(\"storage\", o)\n            })[\"V.useEffect\"];\n        }\n    }[\"V.useEffect\"], [\n        f\n    ]), react__WEBPACK_IMPORTED_MODULE_0__.useEffect({\n        \"V.useEffect\": ()=>{\n            S(e != null ? e : c);\n        }\n    }[\"V.useEffect\"], [\n        e,\n        c\n    ]);\n    let Q = react__WEBPACK_IMPORTED_MODULE_0__.useMemo({\n        \"V.useMemo[Q]\": ()=>({\n                theme: c,\n                setTheme: f,\n                forcedTheme: e,\n                resolvedTheme: c === \"system\" ? T : c,\n                themes: s ? [\n                    ...a,\n                    \"system\"\n                ] : a,\n                systemTheme: s ? T : void 0\n            })\n    }[\"V.useMemo[Q]\"], [\n        c,\n        f,\n        e,\n        T,\n        s,\n        a\n    ]);\n    return /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.createElement(x.Provider, {\n        value: Q\n    }, /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.createElement(_, {\n        forcedTheme: e,\n        storageKey: m,\n        attribute: h,\n        enableSystem: s,\n        enableColorScheme: u,\n        defaultTheme: l,\n        value: d,\n        themes: a,\n        nonce: p,\n        scriptProps: R\n    }), w);\n}, _ = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.memo((param)=>{\n    let { forcedTheme: e, storageKey: i, attribute: s, enableSystem: u, enableColorScheme: m, defaultTheme: a, value: l, themes: h, nonce: d, scriptProps: w } = param;\n    let p = JSON.stringify([\n        s,\n        i,\n        a,\n        e,\n        h,\n        l,\n        u,\n        m\n    ]).slice(1, -1);\n    return /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.createElement(\"script\", {\n        ...w,\n        suppressHydrationWarning: !0,\n        nonce:  false ? 0 : \"\",\n        dangerouslySetInnerHTML: {\n            __html: \"(\".concat(M.toString(), \")(\").concat(p, \")\")\n        }\n    });\n}), H = (e, i)=>{\n    if (O) return;\n    let s;\n    try {\n        s = localStorage.getItem(e) || void 0;\n    } catch (u) {}\n    return s || i;\n}, W = (e)=>{\n    let i = document.createElement(\"style\");\n    return e && i.setAttribute(\"nonce\", e), i.appendChild(document.createTextNode(\"*,*::before,*::after{-webkit-transition:none!important;-moz-transition:none!important;-o-transition:none!important;-ms-transition:none!important;transition:none!important}\")), document.head.appendChild(i), ()=>{\n        window.getComputedStyle(document.body), setTimeout(()=>{\n            document.head.removeChild(i);\n        }, 1);\n    };\n}, E = (e)=>(e || (e = window.matchMedia(I)), e.matches ? \"dark\" : \"light\");\n_s(z, \"gDsCjeeItUuvgOWf1v4qoK9RF6k=\");\n_s1(J, \"gDsCjeeItUuvgOWf1v4qoK9RF6k=\");\n_s2(V, \"UCkmxL+2pKwquH5a3QithkhUKcE=\");\n\nvar _c;\n$RefreshReg$(_c, \"M\");\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/next-themes/dist/index.mjs\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CPC%5C%5CDownloads%5C%5CNew%20folder%20(22)%5C%5Cviral-clip-generator%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%5C%5C%5C%5Capp%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Inter%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22inter%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CPC%5C%5CDownloads%5C%5CNew%20folder%20(22)%5C%5Cviral-clip-generator%5C%5Csrc%5C%5Capp%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CPC%5C%5CDownloads%5C%5CNew%20folder%20(22)%5C%5Cviral-clip-generator%5C%5Csrc%5C%5Ccomponents%5C%5Ctheme-provider.tsx%22%2C%22ids%22%3A%5B%22ThemeProvider%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CPC%5C%5CDownloads%5C%5CNew%20folder%20(22)%5C%5Cviral-clip-generator%5C%5Csrc%5C%5Ccomponents%5C%5Cui%5C%5Csonner.tsx%22%2C%22ids%22%3A%5B%22Toaster%22%5D%7D&server=false!":
/*!**********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CPC%5C%5CDownloads%5C%5CNew%20folder%20(22)%5C%5Cviral-clip-generator%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%5C%5C%5C%5Capp%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Inter%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22inter%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CPC%5C%5CDownloads%5C%5CNew%20folder%20(22)%5C%5Cviral-clip-generator%5C%5Csrc%5C%5Capp%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CPC%5C%5CDownloads%5C%5CNew%20folder%20(22)%5C%5Cviral-clip-generator%5C%5Csrc%5C%5Ccomponents%5C%5Ctheme-provider.tsx%22%2C%22ids%22%3A%5B%22ThemeProvider%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CPC%5C%5CDownloads%5C%5CNew%20folder%20(22)%5C%5Cviral-clip-generator%5C%5Csrc%5C%5Ccomponents%5C%5Cui%5C%5Csonner.tsx%22%2C%22ids%22%3A%5B%22Toaster%22%5D%7D&server=false! ***!
  \**********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval(__webpack_require__.ts("Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/font/google/target.css?{\"path\":\"src\\\\app\\\\layout.tsx\",\"import\":\"Inter\",\"arguments\":[{\"subsets\":[\"latin\"]}],\"variableName\":\"inter\"} */ \"(app-pages-browser)/./node_modules/next/font/google/target.css?{\\\"path\\\":\\\"src\\\\\\\\app\\\\\\\\layout.tsx\\\",\\\"import\\\":\\\"Inter\\\",\\\"arguments\\\":[{\\\"subsets\\\":[\\\"latin\\\"]}],\\\"variableName\\\":\\\"inter\\\"}\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/globals.css */ \"(app-pages-browser)/./src/app/globals.css\"));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/components/theme-provider.tsx */ \"(app-pages-browser)/./src/components/theme-provider.tsx\"));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/components/ui/sonner.tsx */ \"(app-pages-browser)/./src/components/ui/sonner.tsx\"));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CPC%5C%5CDownloads%5C%5CNew%20folder%20(22)%5C%5Cviral-clip-generator%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%5C%5C%5C%5Capp%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Inter%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22inter%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CPC%5C%5CDownloads%5C%5CNew%20folder%20(22)%5C%5Cviral-clip-generator%5C%5Csrc%5C%5Capp%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CPC%5C%5CDownloads%5C%5CNew%20folder%20(22)%5C%5Cviral-clip-generator%5C%5Csrc%5C%5Ccomponents%5C%5Ctheme-provider.tsx%22%2C%22ids%22%3A%5B%22ThemeProvider%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CPC%5C%5CDownloads%5C%5CNew%20folder%20(22)%5C%5Cviral-clip-generator%5C%5Csrc%5C%5Ccomponents%5C%5Cui%5C%5Csonner.tsx%22%2C%22ids%22%3A%5B%22Toaster%22%5D%7D&server=false!\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/next/dist/compiled/mini-css-extract-plugin/hmr/hotModuleReplacement.js":
/*!*********************************************************************************************!*\
  !*** ./node_modules/next/dist/compiled/mini-css-extract-plugin/hmr/hotModuleReplacement.js ***!
  \*********************************************************************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

eval(__webpack_require__.ts("var __dirname = \"/\";\n(()=>{\"use strict\";var e={529:(e,r,t)=>{var n=t(191);var i=Object.create(null);var a=typeof document===\"undefined\";var o=Array.prototype.forEach;function debounce(e,r){var t=0;return function(){var n=this;var i=arguments;var a=function functionCall(){return e.apply(n,i)};clearTimeout(t);t=setTimeout(a,r)}}function noop(){}function getCurrentScriptUrl(e){var r=i[e];if(!r){if(document.currentScript){r=document.currentScript.src}else{var t=document.getElementsByTagName(\"script\");var a=t[t.length-1];if(a){r=a.src}}i[e]=r}return function(e){if(!r){return null}var t=r.split(/([^\\\\/]+)\\.js$/);var i=t&&t[1];if(!i){return[r.replace(\".js\",\".css\")]}if(!e){return[r.replace(\".js\",\".css\")]}return e.split(\",\").map((function(e){var t=new RegExp(\"\".concat(i,\"\\\\.js$\"),\"g\");return n(r.replace(t,\"\".concat(e.replace(/{fileName}/g,i),\".css\")))}))}}function updateCss(e,r){if(!r){if(!e.href){return}r=e.href.split(\"?\")[0]}if(!isUrlRequest(r)){return}if(e.isLoaded===false){return}if(!r||!(r.indexOf(\".css\")>-1)){return}e.visited=true;var t=e.cloneNode();t.isLoaded=false;t.addEventListener(\"load\",(function(){if(t.isLoaded){return}t.isLoaded=true;e.parentNode.removeChild(e)}));t.addEventListener(\"error\",(function(){if(t.isLoaded){return}t.isLoaded=true;e.parentNode.removeChild(e)}));t.href=\"\".concat(r,\"?\").concat(Date.now());if(e.nextSibling){e.parentNode.insertBefore(t,e.nextSibling)}else{e.parentNode.appendChild(t)}}function getReloadUrl(e,r){var t;e=n(e,{stripWWW:false});r.some((function(n){if(e.indexOf(r)>-1){t=n}}));return t}function reloadStyle(e){if(!e){return false}var r=document.querySelectorAll(\"link\");var t=false;o.call(r,(function(r){if(!r.href){return}var n=getReloadUrl(r.href,e);if(!isUrlRequest(n)){return}if(r.visited===true){return}if(n){updateCss(r,n);t=true}}));return t}function reloadAll(){var e=document.querySelectorAll(\"link\");o.call(e,(function(e){if(e.visited===true){return}updateCss(e)}))}function isUrlRequest(e){if(!/^[a-zA-Z][a-zA-Z\\d+\\-.]*:/.test(e)){return false}return true}e.exports=function(e,r){if(a){console.log(\"no window.document found, will not HMR CSS\");return noop}var t=getCurrentScriptUrl(e);function update(){var e=t(r.filename);var n=reloadStyle(e);if(r.locals){console.log(\"[HMR] Detected local css modules. Reload all css\");reloadAll();return}if(n){console.log(\"[HMR] css reload %s\",e.join(\" \"))}else{console.log(\"[HMR] Reload all css\");reloadAll()}}return debounce(update,50)}},191:e=>{function normalizeUrl(e){return e.reduce((function(e,r){switch(r){case\"..\":e.pop();break;case\".\":break;default:e.push(r)}return e}),[]).join(\"/\")}e.exports=function(e){e=e.trim();if(/^data:/i.test(e)){return e}var r=e.indexOf(\"//\")!==-1?e.split(\"//\")[0]+\"//\":\"\";var t=e.replace(new RegExp(r,\"i\"),\"\").split(\"/\");var n=t[0].toLowerCase().replace(/\\.$/,\"\");t[0]=\"\";var i=normalizeUrl(t);return r+n+i}}};var r={};function __nccwpck_require__(t){var n=r[t];if(n!==undefined){return n.exports}var i=r[t]={exports:{}};var a=true;try{e[t](i,i.exports,__nccwpck_require__);a=false}finally{if(a)delete r[t]}return i.exports}if(typeof __nccwpck_require__!==\"undefined\")__nccwpck_require__.ab=__dirname+\"/\";var t=__nccwpck_require__(529);module.exports=t})();//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/next/dist/compiled/mini-css-extract-plugin/hmr/hotModuleReplacement.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/next/dist/compiled/react/cjs/react-jsx-dev-runtime.development.js":
/*!****************************************************************************************!*\
  !*** ./node_modules/next/dist/compiled/react/cjs/react-jsx-dev-runtime.development.js ***!
  \****************************************************************************************/
/***/ ((__unused_webpack_module, exports, __webpack_require__) => {

"use strict";
eval(__webpack_require__.ts("/**\n * @license React\n * react-jsx-dev-runtime.development.js\n *\n * Copyright (c) Meta Platforms, Inc. and affiliates.\n *\n * This source code is licensed under the MIT license found in the\n * LICENSE file in the root directory of this source tree.\n */\n\n\n true &&\n  (function () {\n    function getComponentNameFromType(type) {\n      if (null == type) return null;\n      if (\"function\" === typeof type)\n        return type.$$typeof === REACT_CLIENT_REFERENCE\n          ? null\n          : type.displayName || type.name || null;\n      if (\"string\" === typeof type) return type;\n      switch (type) {\n        case REACT_FRAGMENT_TYPE:\n          return \"Fragment\";\n        case REACT_PROFILER_TYPE:\n          return \"Profiler\";\n        case REACT_STRICT_MODE_TYPE:\n          return \"StrictMode\";\n        case REACT_SUSPENSE_TYPE:\n          return \"Suspense\";\n        case REACT_SUSPENSE_LIST_TYPE:\n          return \"SuspenseList\";\n        case REACT_ACTIVITY_TYPE:\n          return \"Activity\";\n      }\n      if (\"object\" === typeof type)\n        switch (\n          (\"number\" === typeof type.tag &&\n            console.error(\n              \"Received an unexpected object in getComponentNameFromType(). This is likely a bug in React. Please file an issue.\"\n            ),\n          type.$$typeof)\n        ) {\n          case REACT_PORTAL_TYPE:\n            return \"Portal\";\n          case REACT_CONTEXT_TYPE:\n            return (type.displayName || \"Context\") + \".Provider\";\n          case REACT_CONSUMER_TYPE:\n            return (type._context.displayName || \"Context\") + \".Consumer\";\n          case REACT_FORWARD_REF_TYPE:\n            var innerType = type.render;\n            type = type.displayName;\n            type ||\n              ((type = innerType.displayName || innerType.name || \"\"),\n              (type = \"\" !== type ? \"ForwardRef(\" + type + \")\" : \"ForwardRef\"));\n            return type;\n          case REACT_MEMO_TYPE:\n            return (\n              (innerType = type.displayName || null),\n              null !== innerType\n                ? innerType\n                : getComponentNameFromType(type.type) || \"Memo\"\n            );\n          case REACT_LAZY_TYPE:\n            innerType = type._payload;\n            type = type._init;\n            try {\n              return getComponentNameFromType(type(innerType));\n            } catch (x) {}\n        }\n      return null;\n    }\n    function testStringCoercion(value) {\n      return \"\" + value;\n    }\n    function checkKeyStringCoercion(value) {\n      try {\n        testStringCoercion(value);\n        var JSCompiler_inline_result = !1;\n      } catch (e) {\n        JSCompiler_inline_result = !0;\n      }\n      if (JSCompiler_inline_result) {\n        JSCompiler_inline_result = console;\n        var JSCompiler_temp_const = JSCompiler_inline_result.error;\n        var JSCompiler_inline_result$jscomp$0 =\n          (\"function\" === typeof Symbol &&\n            Symbol.toStringTag &&\n            value[Symbol.toStringTag]) ||\n          value.constructor.name ||\n          \"Object\";\n        JSCompiler_temp_const.call(\n          JSCompiler_inline_result,\n          \"The provided key is an unsupported type %s. This value must be coerced to a string before using it here.\",\n          JSCompiler_inline_result$jscomp$0\n        );\n        return testStringCoercion(value);\n      }\n    }\n    function getTaskName(type) {\n      if (type === REACT_FRAGMENT_TYPE) return \"<>\";\n      if (\n        \"object\" === typeof type &&\n        null !== type &&\n        type.$$typeof === REACT_LAZY_TYPE\n      )\n        return \"<...>\";\n      try {\n        var name = getComponentNameFromType(type);\n        return name ? \"<\" + name + \">\" : \"<...>\";\n      } catch (x) {\n        return \"<...>\";\n      }\n    }\n    function getOwner() {\n      var dispatcher = ReactSharedInternals.A;\n      return null === dispatcher ? null : dispatcher.getOwner();\n    }\n    function UnknownOwner() {\n      return Error(\"react-stack-top-frame\");\n    }\n    function hasValidKey(config) {\n      if (hasOwnProperty.call(config, \"key\")) {\n        var getter = Object.getOwnPropertyDescriptor(config, \"key\").get;\n        if (getter && getter.isReactWarning) return !1;\n      }\n      return void 0 !== config.key;\n    }\n    function defineKeyPropWarningGetter(props, displayName) {\n      function warnAboutAccessingKey() {\n        specialPropKeyWarningShown ||\n          ((specialPropKeyWarningShown = !0),\n          console.error(\n            \"%s: `key` is not a prop. Trying to access it will result in `undefined` being returned. If you need to access the same value within the child component, you should pass it as a different prop. (https://react.dev/link/special-props)\",\n            displayName\n          ));\n      }\n      warnAboutAccessingKey.isReactWarning = !0;\n      Object.defineProperty(props, \"key\", {\n        get: warnAboutAccessingKey,\n        configurable: !0\n      });\n    }\n    function elementRefGetterWithDeprecationWarning() {\n      var componentName = getComponentNameFromType(this.type);\n      didWarnAboutElementRef[componentName] ||\n        ((didWarnAboutElementRef[componentName] = !0),\n        console.error(\n          \"Accessing element.ref was removed in React 19. ref is now a regular prop. It will be removed from the JSX Element type in a future release.\"\n        ));\n      componentName = this.props.ref;\n      return void 0 !== componentName ? componentName : null;\n    }\n    function ReactElement(\n      type,\n      key,\n      self,\n      source,\n      owner,\n      props,\n      debugStack,\n      debugTask\n    ) {\n      self = props.ref;\n      type = {\n        $$typeof: REACT_ELEMENT_TYPE,\n        type: type,\n        key: key,\n        props: props,\n        _owner: owner\n      };\n      null !== (void 0 !== self ? self : null)\n        ? Object.defineProperty(type, \"ref\", {\n            enumerable: !1,\n            get: elementRefGetterWithDeprecationWarning\n          })\n        : Object.defineProperty(type, \"ref\", { enumerable: !1, value: null });\n      type._store = {};\n      Object.defineProperty(type._store, \"validated\", {\n        configurable: !1,\n        enumerable: !1,\n        writable: !0,\n        value: 0\n      });\n      Object.defineProperty(type, \"_debugInfo\", {\n        configurable: !1,\n        enumerable: !1,\n        writable: !0,\n        value: null\n      });\n      Object.defineProperty(type, \"_debugStack\", {\n        configurable: !1,\n        enumerable: !1,\n        writable: !0,\n        value: debugStack\n      });\n      Object.defineProperty(type, \"_debugTask\", {\n        configurable: !1,\n        enumerable: !1,\n        writable: !0,\n        value: debugTask\n      });\n      Object.freeze && (Object.freeze(type.props), Object.freeze(type));\n      return type;\n    }\n    function jsxDEVImpl(\n      type,\n      config,\n      maybeKey,\n      isStaticChildren,\n      source,\n      self,\n      debugStack,\n      debugTask\n    ) {\n      var children = config.children;\n      if (void 0 !== children)\n        if (isStaticChildren)\n          if (isArrayImpl(children)) {\n            for (\n              isStaticChildren = 0;\n              isStaticChildren < children.length;\n              isStaticChildren++\n            )\n              validateChildKeys(children[isStaticChildren]);\n            Object.freeze && Object.freeze(children);\n          } else\n            console.error(\n              \"React.jsx: Static children should always be an array. You are likely explicitly calling React.jsxs or React.jsxDEV. Use the Babel transform instead.\"\n            );\n        else validateChildKeys(children);\n      if (hasOwnProperty.call(config, \"key\")) {\n        children = getComponentNameFromType(type);\n        var keys = Object.keys(config).filter(function (k) {\n          return \"key\" !== k;\n        });\n        isStaticChildren =\n          0 < keys.length\n            ? \"{key: someKey, \" + keys.join(\": ..., \") + \": ...}\"\n            : \"{key: someKey}\";\n        didWarnAboutKeySpread[children + isStaticChildren] ||\n          ((keys =\n            0 < keys.length ? \"{\" + keys.join(\": ..., \") + \": ...}\" : \"{}\"),\n          console.error(\n            'A props object containing a \"key\" prop is being spread into JSX:\\n  let props = %s;\\n  <%s {...props} />\\nReact keys must be passed directly to JSX without using spread:\\n  let props = %s;\\n  <%s key={someKey} {...props} />',\n            isStaticChildren,\n            children,\n            keys,\n            children\n          ),\n          (didWarnAboutKeySpread[children + isStaticChildren] = !0));\n      }\n      children = null;\n      void 0 !== maybeKey &&\n        (checkKeyStringCoercion(maybeKey), (children = \"\" + maybeKey));\n      hasValidKey(config) &&\n        (checkKeyStringCoercion(config.key), (children = \"\" + config.key));\n      if (\"key\" in config) {\n        maybeKey = {};\n        for (var propName in config)\n          \"key\" !== propName && (maybeKey[propName] = config[propName]);\n      } else maybeKey = config;\n      children &&\n        defineKeyPropWarningGetter(\n          maybeKey,\n          \"function\" === typeof type\n            ? type.displayName || type.name || \"Unknown\"\n            : type\n        );\n      return ReactElement(\n        type,\n        children,\n        self,\n        source,\n        getOwner(),\n        maybeKey,\n        debugStack,\n        debugTask\n      );\n    }\n    function validateChildKeys(node) {\n      \"object\" === typeof node &&\n        null !== node &&\n        node.$$typeof === REACT_ELEMENT_TYPE &&\n        node._store &&\n        (node._store.validated = 1);\n    }\n    var React = __webpack_require__(/*! next/dist/compiled/react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\"),\n      REACT_ELEMENT_TYPE = Symbol.for(\"react.transitional.element\"),\n      REACT_PORTAL_TYPE = Symbol.for(\"react.portal\"),\n      REACT_FRAGMENT_TYPE = Symbol.for(\"react.fragment\"),\n      REACT_STRICT_MODE_TYPE = Symbol.for(\"react.strict_mode\"),\n      REACT_PROFILER_TYPE = Symbol.for(\"react.profiler\");\n    Symbol.for(\"react.provider\");\n    var REACT_CONSUMER_TYPE = Symbol.for(\"react.consumer\"),\n      REACT_CONTEXT_TYPE = Symbol.for(\"react.context\"),\n      REACT_FORWARD_REF_TYPE = Symbol.for(\"react.forward_ref\"),\n      REACT_SUSPENSE_TYPE = Symbol.for(\"react.suspense\"),\n      REACT_SUSPENSE_LIST_TYPE = Symbol.for(\"react.suspense_list\"),\n      REACT_MEMO_TYPE = Symbol.for(\"react.memo\"),\n      REACT_LAZY_TYPE = Symbol.for(\"react.lazy\"),\n      REACT_ACTIVITY_TYPE = Symbol.for(\"react.activity\"),\n      REACT_CLIENT_REFERENCE = Symbol.for(\"react.client.reference\"),\n      ReactSharedInternals =\n        React.__CLIENT_INTERNALS_DO_NOT_USE_OR_WARN_USERS_THEY_CANNOT_UPGRADE,\n      hasOwnProperty = Object.prototype.hasOwnProperty,\n      isArrayImpl = Array.isArray,\n      createTask = console.createTask\n        ? console.createTask\n        : function () {\n            return null;\n          };\n    React = {\n      \"react-stack-bottom-frame\": function (callStackForError) {\n        return callStackForError();\n      }\n    };\n    var specialPropKeyWarningShown;\n    var didWarnAboutElementRef = {};\n    var unknownOwnerDebugStack = React[\"react-stack-bottom-frame\"].bind(\n      React,\n      UnknownOwner\n    )();\n    var unknownOwnerDebugTask = createTask(getTaskName(UnknownOwner));\n    var didWarnAboutKeySpread = {};\n    exports.Fragment = REACT_FRAGMENT_TYPE;\n    exports.jsxDEV = function (\n      type,\n      config,\n      maybeKey,\n      isStaticChildren,\n      source,\n      self\n    ) {\n      var trackActualOwner =\n        1e4 > ReactSharedInternals.recentlyCreatedOwnerStacks++;\n      return jsxDEVImpl(\n        type,\n        config,\n        maybeKey,\n        isStaticChildren,\n        source,\n        self,\n        trackActualOwner\n          ? Error(\"react-stack-top-frame\")\n          : unknownOwnerDebugStack,\n        trackActualOwner ? createTask(getTaskName(type)) : unknownOwnerDebugTask\n      );\n    };\n  })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/next/dist/compiled/react/cjs/react-jsx-dev-runtime.development.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js":
/*!******************************************************************!*\
  !*** ./node_modules/next/dist/compiled/react/jsx-dev-runtime.js ***!
  \******************************************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

"use strict";
eval(__webpack_require__.ts("\n\nif (false) {} else {\n  module.exports = __webpack_require__(/*! ./cjs/react-jsx-dev-runtime.development.js */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/cjs/react-jsx-dev-runtime.development.js\");\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL25vZGVfbW9kdWxlcy9uZXh0L2Rpc3QvY29tcGlsZWQvcmVhY3QvanN4LWRldi1ydW50aW1lLmpzIiwibWFwcGluZ3MiOiJBQUFhOztBQUViLElBQUksS0FBcUMsRUFBRSxFQUUxQyxDQUFDO0FBQ0YsRUFBRSw4TEFBc0U7QUFDeEUiLCJzb3VyY2VzIjpbIkM6XFxVc2Vyc1xcUENcXERvd25sb2Fkc1xcTmV3IGZvbGRlciAoMjIpXFx2aXJhbC1jbGlwLWdlbmVyYXRvclxcbm9kZV9tb2R1bGVzXFxuZXh0XFxkaXN0XFxjb21waWxlZFxccmVhY3RcXGpzeC1kZXYtcnVudGltZS5qcyJdLCJzb3VyY2VzQ29udGVudCI6WyIndXNlIHN0cmljdCc7XG5cbmlmIChwcm9jZXNzLmVudi5OT0RFX0VOViA9PT0gJ3Byb2R1Y3Rpb24nKSB7XG4gIG1vZHVsZS5leHBvcnRzID0gcmVxdWlyZSgnLi9janMvcmVhY3QtanN4LWRldi1ydW50aW1lLnByb2R1Y3Rpb24uanMnKTtcbn0gZWxzZSB7XG4gIG1vZHVsZS5leHBvcnRzID0gcmVxdWlyZSgnLi9janMvcmVhY3QtanN4LWRldi1ydW50aW1lLmRldmVsb3BtZW50LmpzJyk7XG59XG4iXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbMF0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/next/font/google/target.css?{\"path\":\"src\\\\app\\\\layout.tsx\",\"import\":\"Inter\",\"arguments\":[{\"subsets\":[\"latin\"]}],\"variableName\":\"inter\"}":
/*!**************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/font/google/target.css?{"path":"src\\app\\layout.tsx","import":"Inter","arguments":[{"subsets":["latin"]}],"variableName":"inter"} ***!
  \**************************************************************************************************************************************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

eval(__webpack_require__.ts("// extracted by mini-css-extract-plugin\nmodule.exports = {\"style\":{\"fontFamily\":\"'Inter', 'Inter Fallback'\",\"fontStyle\":\"normal\"},\"className\":\"__className_e8ce0c\"};\n    if(true) {\n      // 1751722839014\n      var cssReload = __webpack_require__(/*! ./node_modules/next/dist/compiled/mini-css-extract-plugin/hmr/hotModuleReplacement.js */ \"(app-pages-browser)/./node_modules/next/dist/compiled/mini-css-extract-plugin/hmr/hotModuleReplacement.js\")(module.id, {\"publicPath\":\"/_next/\",\"esModule\":false,\"locals\":true});\n      module.hot.dispose(cssReload);\n      \n    }\n  //# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL25vZGVfbW9kdWxlcy9uZXh0L2ZvbnQvZ29vZ2xlL3RhcmdldC5jc3M/e1wicGF0aFwiOlwic3JjXFxcXGFwcFxcXFxsYXlvdXQudHN4XCIsXCJpbXBvcnRcIjpcIkludGVyXCIsXCJhcmd1bWVudHNcIjpbe1wic3Vic2V0c1wiOltcImxhdGluXCJdfV0sXCJ2YXJpYWJsZU5hbWVcIjpcImludGVyXCJ9IiwibWFwcGluZ3MiOiJBQUFBO0FBQ0Esa0JBQWtCLFNBQVMsOERBQThEO0FBQ3pGLE9BQU8sSUFBVTtBQUNqQjtBQUNBLHNCQUFzQixtQkFBTyxDQUFDLHdNQUFnSixjQUFjLHNEQUFzRDtBQUNsUCxNQUFNLFVBQVU7QUFDaEI7QUFDQTtBQUNBIiwic291cmNlcyI6WyJDOlxcVXNlcnNcXFBDXFxEb3dubG9hZHNcXE5ldyBmb2xkZXIgKDIyKVxcdmlyYWwtY2xpcC1nZW5lcmF0b3JcXG5vZGVfbW9kdWxlc1xcbmV4dFxcZm9udFxcZ29vZ2xlXFx0YXJnZXQuY3NzP3tcInBhdGhcIjpcInNyY1xcYXBwXFxsYXlvdXQudHN4XCIsXCJpbXBvcnRcIjpcIkludGVyXCIsXCJhcmd1bWVudHNcIjpbe1wic3Vic2V0c1wiOltcImxhdGluXCJdfV0sXCJ2YXJpYWJsZU5hbWVcIjpcImludGVyXCJ9fGFwcC1wYWdlcy1icm93c2VyIl0sInNvdXJjZXNDb250ZW50IjpbIi8vIGV4dHJhY3RlZCBieSBtaW5pLWNzcy1leHRyYWN0LXBsdWdpblxubW9kdWxlLmV4cG9ydHMgPSB7XCJzdHlsZVwiOntcImZvbnRGYW1pbHlcIjpcIidJbnRlcicsICdJbnRlciBGYWxsYmFjaydcIixcImZvbnRTdHlsZVwiOlwibm9ybWFsXCJ9LFwiY2xhc3NOYW1lXCI6XCJfX2NsYXNzTmFtZV9lOGNlMGNcIn07XG4gICAgaWYobW9kdWxlLmhvdCkge1xuICAgICAgLy8gMTc1MTcyMjgzOTAxNFxuICAgICAgdmFyIGNzc1JlbG9hZCA9IHJlcXVpcmUoXCJDOi9Vc2Vycy9QQy9Eb3dubG9hZHMvTmV3IGZvbGRlciAoMjIpL3ZpcmFsLWNsaXAtZ2VuZXJhdG9yL25vZGVfbW9kdWxlcy9uZXh0L2Rpc3QvY29tcGlsZWQvbWluaS1jc3MtZXh0cmFjdC1wbHVnaW4vaG1yL2hvdE1vZHVsZVJlcGxhY2VtZW50LmpzXCIpKG1vZHVsZS5pZCwge1wicHVibGljUGF0aFwiOlwiL19uZXh0L1wiLFwiZXNNb2R1bGVcIjpmYWxzZSxcImxvY2Fsc1wiOnRydWV9KTtcbiAgICAgIG1vZHVsZS5ob3QuZGlzcG9zZShjc3NSZWxvYWQpO1xuICAgICAgXG4gICAgfVxuICAiXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbMF0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/next/font/google/target.css?{\"path\":\"src\\\\app\\\\layout.tsx\",\"import\":\"Inter\",\"arguments\":[{\"subsets\":[\"latin\"]}],\"variableName\":\"inter\"}\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/sonner/dist/index.mjs":
/*!********************************************!*\
  !*** ./node_modules/sonner/dist/index.mjs ***!
  \********************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

"use strict";
eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Toaster: () => (/* binding */ Toaster),\n/* harmony export */   toast: () => (/* binding */ toast),\n/* harmony export */   useSonner: () => (/* binding */ useSonner)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react_dom__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react-dom */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react-dom/index.js\");\n/* __next_internal_client_entry_do_not_use__ Toaster,toast,useSonner auto */ var _s = $RefreshSig$(), _s1 = $RefreshSig$(), _s2 = $RefreshSig$(), _s3 = $RefreshSig$();\nfunction __insertCSS(code) {\n    if (!code || typeof document == 'undefined') return;\n    let head = document.head || document.getElementsByTagName('head')[0];\n    let style = document.createElement('style');\n    style.type = 'text/css';\n    head.appendChild(style);\n    style.styleSheet ? style.styleSheet.cssText = code : style.appendChild(document.createTextNode(code));\n}\n\n\nconst getAsset = (type)=>{\n    switch(type){\n        case 'success':\n            return SuccessIcon;\n        case 'info':\n            return InfoIcon;\n        case 'warning':\n            return WarningIcon;\n        case 'error':\n            return ErrorIcon;\n        default:\n            return null;\n    }\n};\nconst bars = Array(12).fill(0);\nconst Loader = (param)=>{\n    let { visible, className } = param;\n    return /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.createElement(\"div\", {\n        className: [\n            'sonner-loading-wrapper',\n            className\n        ].filter(Boolean).join(' '),\n        \"data-visible\": visible\n    }, /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.createElement(\"div\", {\n        className: \"sonner-spinner\"\n    }, bars.map((_, i)=>/*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.createElement(\"div\", {\n            className: \"sonner-loading-bar\",\n            key: \"spinner-bar-\".concat(i)\n        }))));\n};\n_c = Loader;\nconst SuccessIcon = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.createElement(\"svg\", {\n    xmlns: \"http://www.w3.org/2000/svg\",\n    viewBox: \"0 0 20 20\",\n    fill: \"currentColor\",\n    height: \"20\",\n    width: \"20\"\n}, /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.createElement(\"path\", {\n    fillRule: \"evenodd\",\n    d: \"M10 18a8 8 0 100-16 8 8 0 000 16zm3.857-9.809a.75.75 0 00-1.214-.882l-3.483 4.79-1.88-1.88a.75.75 0 10-1.06 1.061l2.5 2.5a.75.75 0 001.137-.089l4-5.5z\",\n    clipRule: \"evenodd\"\n}));\nconst WarningIcon = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.createElement(\"svg\", {\n    xmlns: \"http://www.w3.org/2000/svg\",\n    viewBox: \"0 0 24 24\",\n    fill: \"currentColor\",\n    height: \"20\",\n    width: \"20\"\n}, /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.createElement(\"path\", {\n    fillRule: \"evenodd\",\n    d: \"M9.401 3.003c1.155-2 4.043-2 5.197 0l7.355 12.748c1.154 2-.29 4.5-2.599 4.5H4.645c-2.309 0-3.752-2.5-2.598-4.5L9.4 3.003zM12 8.25a.75.75 0 01.75.75v3.75a.75.75 0 01-1.5 0V9a.75.75 0 01.75-.75zm0 8.25a.75.75 0 100-********* 0 000 1.5z\",\n    clipRule: \"evenodd\"\n}));\nconst InfoIcon = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.createElement(\"svg\", {\n    xmlns: \"http://www.w3.org/2000/svg\",\n    viewBox: \"0 0 20 20\",\n    fill: \"currentColor\",\n    height: \"20\",\n    width: \"20\"\n}, /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.createElement(\"path\", {\n    fillRule: \"evenodd\",\n    d: \"M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7-4a1 1 0 11-2 0 1 1 0 012 0zM9 9a.75.75 0 000 1.5h.253a.25.25 0 01.244.304l-.459 2.066A1.75 1.75 0 0010.747 15H11a.75.75 0 000-1.5h-.253a.25.25 0 01-.244-.304l.459-2.066A1.75 1.75 0 009.253 9H9z\",\n    clipRule: \"evenodd\"\n}));\nconst ErrorIcon = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.createElement(\"svg\", {\n    xmlns: \"http://www.w3.org/2000/svg\",\n    viewBox: \"0 0 20 20\",\n    fill: \"currentColor\",\n    height: \"20\",\n    width: \"20\"\n}, /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.createElement(\"path\", {\n    fillRule: \"evenodd\",\n    d: \"M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-8-5a.75.75 0 01.75.75v4.5a.75.75 0 01-1.5 0v-4.5A.75.75 0 0110 5zm0 10a1 1 0 100-2 1 1 0 000 2z\",\n    clipRule: \"evenodd\"\n}));\nconst CloseIcon = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.createElement(\"svg\", {\n    xmlns: \"http://www.w3.org/2000/svg\",\n    width: \"12\",\n    height: \"12\",\n    viewBox: \"0 0 24 24\",\n    fill: \"none\",\n    stroke: \"currentColor\",\n    strokeWidth: \"1.5\",\n    strokeLinecap: \"round\",\n    strokeLinejoin: \"round\"\n}, /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.createElement(\"line\", {\n    x1: \"18\",\n    y1: \"6\",\n    x2: \"6\",\n    y2: \"18\"\n}), /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.createElement(\"line\", {\n    x1: \"6\",\n    y1: \"6\",\n    x2: \"18\",\n    y2: \"18\"\n}));\nconst useIsDocumentHidden = ()=>{\n    _s();\n    const [isDocumentHidden, setIsDocumentHidden] = react__WEBPACK_IMPORTED_MODULE_0__.useState(document.hidden);\n    react__WEBPACK_IMPORTED_MODULE_0__.useEffect({\n        \"useIsDocumentHidden.useEffect\": ()=>{\n            const callback = {\n                \"useIsDocumentHidden.useEffect.callback\": ()=>{\n                    setIsDocumentHidden(document.hidden);\n                }\n            }[\"useIsDocumentHidden.useEffect.callback\"];\n            document.addEventListener('visibilitychange', callback);\n            return ({\n                \"useIsDocumentHidden.useEffect\": ()=>window.removeEventListener('visibilitychange', callback)\n            })[\"useIsDocumentHidden.useEffect\"];\n        }\n    }[\"useIsDocumentHidden.useEffect\"], []);\n    return isDocumentHidden;\n};\n_s(useIsDocumentHidden, \"RJwWklAunJjdVVAElZ/SoraKxVU=\");\nlet toastsCounter = 1;\nclass Observer {\n    constructor(){\n        // We use arrow functions to maintain the correct `this` reference\n        this.subscribe = (subscriber)=>{\n            this.subscribers.push(subscriber);\n            return ()=>{\n                const index = this.subscribers.indexOf(subscriber);\n                this.subscribers.splice(index, 1);\n            };\n        };\n        this.publish = (data)=>{\n            this.subscribers.forEach((subscriber)=>subscriber(data));\n        };\n        this.addToast = (data)=>{\n            this.publish(data);\n            this.toasts = [\n                ...this.toasts,\n                data\n            ];\n        };\n        this.create = (data)=>{\n            var _data_id;\n            const { message, ...rest } = data;\n            const id = typeof (data == null ? void 0 : data.id) === 'number' || ((_data_id = data.id) == null ? void 0 : _data_id.length) > 0 ? data.id : toastsCounter++;\n            const alreadyExists = this.toasts.find((toast)=>{\n                return toast.id === id;\n            });\n            const dismissible = data.dismissible === undefined ? true : data.dismissible;\n            if (this.dismissedToasts.has(id)) {\n                this.dismissedToasts.delete(id);\n            }\n            if (alreadyExists) {\n                this.toasts = this.toasts.map((toast)=>{\n                    if (toast.id === id) {\n                        this.publish({\n                            ...toast,\n                            ...data,\n                            id,\n                            title: message\n                        });\n                        return {\n                            ...toast,\n                            ...data,\n                            id,\n                            dismissible,\n                            title: message\n                        };\n                    }\n                    return toast;\n                });\n            } else {\n                this.addToast({\n                    title: message,\n                    ...rest,\n                    dismissible,\n                    id\n                });\n            }\n            return id;\n        };\n        this.dismiss = (id)=>{\n            if (id) {\n                this.dismissedToasts.add(id);\n                requestAnimationFrame(()=>this.subscribers.forEach((subscriber)=>subscriber({\n                            id,\n                            dismiss: true\n                        })));\n            } else {\n                this.toasts.forEach((toast)=>{\n                    this.subscribers.forEach((subscriber)=>subscriber({\n                            id: toast.id,\n                            dismiss: true\n                        }));\n                });\n            }\n            return id;\n        };\n        this.message = (message, data)=>{\n            return this.create({\n                ...data,\n                message\n            });\n        };\n        this.error = (message, data)=>{\n            return this.create({\n                ...data,\n                message,\n                type: 'error'\n            });\n        };\n        this.success = (message, data)=>{\n            return this.create({\n                ...data,\n                type: 'success',\n                message\n            });\n        };\n        this.info = (message, data)=>{\n            return this.create({\n                ...data,\n                type: 'info',\n                message\n            });\n        };\n        this.warning = (message, data)=>{\n            return this.create({\n                ...data,\n                type: 'warning',\n                message\n            });\n        };\n        this.loading = (message, data)=>{\n            return this.create({\n                ...data,\n                type: 'loading',\n                message\n            });\n        };\n        this.promise = (promise, data)=>{\n            if (!data) {\n                // Nothing to show\n                return;\n            }\n            let id = undefined;\n            if (data.loading !== undefined) {\n                id = this.create({\n                    ...data,\n                    promise,\n                    type: 'loading',\n                    message: data.loading,\n                    description: typeof data.description !== 'function' ? data.description : undefined\n                });\n            }\n            const p = Promise.resolve(promise instanceof Function ? promise() : promise);\n            let shouldDismiss = id !== undefined;\n            let result;\n            const originalPromise = p.then(async (response)=>{\n                result = [\n                    'resolve',\n                    response\n                ];\n                const isReactElementResponse = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.isValidElement(response);\n                if (isReactElementResponse) {\n                    shouldDismiss = false;\n                    this.create({\n                        id,\n                        type: 'default',\n                        message: response\n                    });\n                } else if (isHttpResponse(response) && !response.ok) {\n                    shouldDismiss = false;\n                    const promiseData = typeof data.error === 'function' ? await data.error(\"HTTP error! status: \".concat(response.status)) : data.error;\n                    const description = typeof data.description === 'function' ? await data.description(\"HTTP error! status: \".concat(response.status)) : data.description;\n                    const isExtendedResult = typeof promiseData === 'object' && !/*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.isValidElement(promiseData);\n                    const toastSettings = isExtendedResult ? promiseData : {\n                        message: promiseData\n                    };\n                    this.create({\n                        id,\n                        type: 'error',\n                        description,\n                        ...toastSettings\n                    });\n                } else if (response instanceof Error) {\n                    shouldDismiss = false;\n                    const promiseData = typeof data.error === 'function' ? await data.error(response) : data.error;\n                    const description = typeof data.description === 'function' ? await data.description(response) : data.description;\n                    const isExtendedResult = typeof promiseData === 'object' && !/*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.isValidElement(promiseData);\n                    const toastSettings = isExtendedResult ? promiseData : {\n                        message: promiseData\n                    };\n                    this.create({\n                        id,\n                        type: 'error',\n                        description,\n                        ...toastSettings\n                    });\n                } else if (data.success !== undefined) {\n                    shouldDismiss = false;\n                    const promiseData = typeof data.success === 'function' ? await data.success(response) : data.success;\n                    const description = typeof data.description === 'function' ? await data.description(response) : data.description;\n                    const isExtendedResult = typeof promiseData === 'object' && !/*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.isValidElement(promiseData);\n                    const toastSettings = isExtendedResult ? promiseData : {\n                        message: promiseData\n                    };\n                    this.create({\n                        id,\n                        type: 'success',\n                        description,\n                        ...toastSettings\n                    });\n                }\n            }).catch(async (error)=>{\n                result = [\n                    'reject',\n                    error\n                ];\n                if (data.error !== undefined) {\n                    shouldDismiss = false;\n                    const promiseData = typeof data.error === 'function' ? await data.error(error) : data.error;\n                    const description = typeof data.description === 'function' ? await data.description(error) : data.description;\n                    const isExtendedResult = typeof promiseData === 'object' && !/*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.isValidElement(promiseData);\n                    const toastSettings = isExtendedResult ? promiseData : {\n                        message: promiseData\n                    };\n                    this.create({\n                        id,\n                        type: 'error',\n                        description,\n                        ...toastSettings\n                    });\n                }\n            }).finally(()=>{\n                if (shouldDismiss) {\n                    // Toast is still in load state (and will be indefinitely — dismiss it)\n                    this.dismiss(id);\n                    id = undefined;\n                }\n                data.finally == null ? void 0 : data.finally.call(data);\n            });\n            const unwrap = ()=>new Promise((resolve, reject)=>originalPromise.then(()=>result[0] === 'reject' ? reject(result[1]) : resolve(result[1])).catch(reject));\n            if (typeof id !== 'string' && typeof id !== 'number') {\n                // cannot Object.assign on undefined\n                return {\n                    unwrap\n                };\n            } else {\n                return Object.assign(id, {\n                    unwrap\n                });\n            }\n        };\n        this.custom = (jsx, data)=>{\n            const id = (data == null ? void 0 : data.id) || toastsCounter++;\n            this.create({\n                jsx: jsx(id),\n                id,\n                ...data\n            });\n            return id;\n        };\n        this.getActiveToasts = ()=>{\n            return this.toasts.filter((toast)=>!this.dismissedToasts.has(toast.id));\n        };\n        this.subscribers = [];\n        this.toasts = [];\n        this.dismissedToasts = new Set();\n    }\n}\nconst ToastState = new Observer();\n// bind this to the toast function\nconst toastFunction = (message, data)=>{\n    const id = (data == null ? void 0 : data.id) || toastsCounter++;\n    ToastState.addToast({\n        title: message,\n        ...data,\n        id\n    });\n    return id;\n};\nconst isHttpResponse = (data)=>{\n    return data && typeof data === 'object' && 'ok' in data && typeof data.ok === 'boolean' && 'status' in data && typeof data.status === 'number';\n};\nconst basicToast = toastFunction;\nconst getHistory = ()=>ToastState.toasts;\nconst getToasts = ()=>ToastState.getActiveToasts();\n// We use `Object.assign` to maintain the correct types as we would lose them otherwise\nconst toast = Object.assign(basicToast, {\n    success: ToastState.success,\n    info: ToastState.info,\n    warning: ToastState.warning,\n    error: ToastState.error,\n    custom: ToastState.custom,\n    message: ToastState.message,\n    promise: ToastState.promise,\n    dismiss: ToastState.dismiss,\n    loading: ToastState.loading\n}, {\n    getHistory,\n    getToasts\n});\n__insertCSS(\"[data-sonner-toaster][dir=ltr],html[dir=ltr]{--toast-icon-margin-start:-3px;--toast-icon-margin-end:4px;--toast-svg-margin-start:-1px;--toast-svg-margin-end:0px;--toast-button-margin-start:auto;--toast-button-margin-end:0;--toast-close-button-start:0;--toast-close-button-end:unset;--toast-close-button-transform:translate(-35%, -35%)}[data-sonner-toaster][dir=rtl],html[dir=rtl]{--toast-icon-margin-start:4px;--toast-icon-margin-end:-3px;--toast-svg-margin-start:0px;--toast-svg-margin-end:-1px;--toast-button-margin-start:0;--toast-button-margin-end:auto;--toast-close-button-start:unset;--toast-close-button-end:0;--toast-close-button-transform:translate(35%, -35%)}[data-sonner-toaster]{position:fixed;width:var(--width);font-family:ui-sans-serif,system-ui,-apple-system,BlinkMacSystemFont,Segoe UI,Roboto,Helvetica Neue,Arial,Noto Sans,sans-serif,Apple Color Emoji,Segoe UI Emoji,Segoe UI Symbol,Noto Color Emoji;--gray1:hsl(0, 0%, 99%);--gray2:hsl(0, 0%, 97.3%);--gray3:hsl(0, 0%, 95.1%);--gray4:hsl(0, 0%, 93%);--gray5:hsl(0, 0%, 90.9%);--gray6:hsl(0, 0%, 88.7%);--gray7:hsl(0, 0%, 85.8%);--gray8:hsl(0, 0%, 78%);--gray9:hsl(0, 0%, 56.1%);--gray10:hsl(0, 0%, 52.3%);--gray11:hsl(0, 0%, 43.5%);--gray12:hsl(0, 0%, 9%);--border-radius:8px;box-sizing:border-box;padding:0;margin:0;list-style:none;outline:0;z-index:999999999;transition:transform .4s ease}@media (hover:none) and (pointer:coarse){[data-sonner-toaster][data-lifted=true]{transform:none}}[data-sonner-toaster][data-x-position=right]{right:var(--offset-right)}[data-sonner-toaster][data-x-position=left]{left:var(--offset-left)}[data-sonner-toaster][data-x-position=center]{left:50%;transform:translateX(-50%)}[data-sonner-toaster][data-y-position=top]{top:var(--offset-top)}[data-sonner-toaster][data-y-position=bottom]{bottom:var(--offset-bottom)}[data-sonner-toast]{--y:translateY(100%);--lift-amount:calc(var(--lift) * var(--gap));z-index:var(--z-index);position:absolute;opacity:0;transform:var(--y);touch-action:none;transition:transform .4s,opacity .4s,height .4s,box-shadow .2s;box-sizing:border-box;outline:0;overflow-wrap:anywhere}[data-sonner-toast][data-styled=true]{padding:16px;background:var(--normal-bg);border:1px solid var(--normal-border);color:var(--normal-text);border-radius:var(--border-radius);box-shadow:0 4px 12px rgba(0,0,0,.1);width:var(--width);font-size:13px;display:flex;align-items:center;gap:6px}[data-sonner-toast]:focus-visible{box-shadow:0 4px 12px rgba(0,0,0,.1),0 0 0 2px rgba(0,0,0,.2)}[data-sonner-toast][data-y-position=top]{top:0;--y:translateY(-100%);--lift:1;--lift-amount:calc(1 * var(--gap))}[data-sonner-toast][data-y-position=bottom]{bottom:0;--y:translateY(100%);--lift:-1;--lift-amount:calc(var(--lift) * var(--gap))}[data-sonner-toast][data-styled=true] [data-description]{font-weight:400;line-height:1.4;color:#3f3f3f}[data-rich-colors=true][data-sonner-toast][data-styled=true] [data-description]{color:inherit}[data-sonner-toaster][data-sonner-theme=dark] [data-description]{color:#e8e8e8}[data-sonner-toast][data-styled=true] [data-title]{font-weight:500;line-height:1.5;color:inherit}[data-sonner-toast][data-styled=true] [data-icon]{display:flex;height:16px;width:16px;position:relative;justify-content:flex-start;align-items:center;flex-shrink:0;margin-left:var(--toast-icon-margin-start);margin-right:var(--toast-icon-margin-end)}[data-sonner-toast][data-promise=true] [data-icon]>svg{opacity:0;transform:scale(.8);transform-origin:center;animation:sonner-fade-in .3s ease forwards}[data-sonner-toast][data-styled=true] [data-icon]>*{flex-shrink:0}[data-sonner-toast][data-styled=true] [data-icon] svg{margin-left:var(--toast-svg-margin-start);margin-right:var(--toast-svg-margin-end)}[data-sonner-toast][data-styled=true] [data-content]{display:flex;flex-direction:column;gap:2px}[data-sonner-toast][data-styled=true] [data-button]{border-radius:4px;padding-left:8px;padding-right:8px;height:24px;font-size:12px;color:var(--normal-bg);background:var(--normal-text);margin-left:var(--toast-button-margin-start);margin-right:var(--toast-button-margin-end);border:none;font-weight:500;cursor:pointer;outline:0;display:flex;align-items:center;flex-shrink:0;transition:opacity .4s,box-shadow .2s}[data-sonner-toast][data-styled=true] [data-button]:focus-visible{box-shadow:0 0 0 2px rgba(0,0,0,.4)}[data-sonner-toast][data-styled=true] [data-button]:first-of-type{margin-left:var(--toast-button-margin-start);margin-right:var(--toast-button-margin-end)}[data-sonner-toast][data-styled=true] [data-cancel]{color:var(--normal-text);background:rgba(0,0,0,.08)}[data-sonner-toaster][data-sonner-theme=dark] [data-sonner-toast][data-styled=true] [data-cancel]{background:rgba(255,255,255,.3)}[data-sonner-toast][data-styled=true] [data-close-button]{position:absolute;left:var(--toast-close-button-start);right:var(--toast-close-button-end);top:0;height:20px;width:20px;display:flex;justify-content:center;align-items:center;padding:0;color:var(--gray12);background:var(--normal-bg);border:1px solid var(--gray4);transform:var(--toast-close-button-transform);border-radius:50%;cursor:pointer;z-index:1;transition:opacity .1s,background .2s,border-color .2s}[data-sonner-toast][data-styled=true] [data-close-button]:focus-visible{box-shadow:0 4px 12px rgba(0,0,0,.1),0 0 0 2px rgba(0,0,0,.2)}[data-sonner-toast][data-styled=true] [data-disabled=true]{cursor:not-allowed}[data-sonner-toast][data-styled=true]:hover [data-close-button]:hover{background:var(--gray2);border-color:var(--gray5)}[data-sonner-toast][data-swiping=true]::before{content:'';position:absolute;left:-100%;right:-100%;height:100%;z-index:-1}[data-sonner-toast][data-y-position=top][data-swiping=true]::before{bottom:50%;transform:scaleY(3) translateY(50%)}[data-sonner-toast][data-y-position=bottom][data-swiping=true]::before{top:50%;transform:scaleY(3) translateY(-50%)}[data-sonner-toast][data-swiping=false][data-removed=true]::before{content:'';position:absolute;inset:0;transform:scaleY(2)}[data-sonner-toast][data-expanded=true]::after{content:'';position:absolute;left:0;height:calc(var(--gap) + 1px);bottom:100%;width:100%}[data-sonner-toast][data-mounted=true]{--y:translateY(0);opacity:1}[data-sonner-toast][data-expanded=false][data-front=false]{--scale:var(--toasts-before) * 0.05 + 1;--y:translateY(calc(var(--lift-amount) * var(--toasts-before))) scale(calc(-1 * var(--scale)));height:var(--front-toast-height)}[data-sonner-toast]>*{transition:opacity .4s}[data-sonner-toast][data-x-position=right]{right:0}[data-sonner-toast][data-x-position=left]{left:0}[data-sonner-toast][data-expanded=false][data-front=false][data-styled=true]>*{opacity:0}[data-sonner-toast][data-visible=false]{opacity:0;pointer-events:none}[data-sonner-toast][data-mounted=true][data-expanded=true]{--y:translateY(calc(var(--lift) * var(--offset)));height:var(--initial-height)}[data-sonner-toast][data-removed=true][data-front=true][data-swipe-out=false]{--y:translateY(calc(var(--lift) * -100%));opacity:0}[data-sonner-toast][data-removed=true][data-front=false][data-swipe-out=false][data-expanded=true]{--y:translateY(calc(var(--lift) * var(--offset) + var(--lift) * -100%));opacity:0}[data-sonner-toast][data-removed=true][data-front=false][data-swipe-out=false][data-expanded=false]{--y:translateY(40%);opacity:0;transition:transform .5s,opacity .2s}[data-sonner-toast][data-removed=true][data-front=false]::before{height:calc(var(--initial-height) + 20%)}[data-sonner-toast][data-swiping=true]{transform:var(--y) translateY(var(--swipe-amount-y,0)) translateX(var(--swipe-amount-x,0));transition:none}[data-sonner-toast][data-swiped=true]{user-select:none}[data-sonner-toast][data-swipe-out=true][data-y-position=bottom],[data-sonner-toast][data-swipe-out=true][data-y-position=top]{animation-duration:.2s;animation-timing-function:ease-out;animation-fill-mode:forwards}[data-sonner-toast][data-swipe-out=true][data-swipe-direction=left]{animation-name:swipe-out-left}[data-sonner-toast][data-swipe-out=true][data-swipe-direction=right]{animation-name:swipe-out-right}[data-sonner-toast][data-swipe-out=true][data-swipe-direction=up]{animation-name:swipe-out-up}[data-sonner-toast][data-swipe-out=true][data-swipe-direction=down]{animation-name:swipe-out-down}@keyframes swipe-out-left{from{transform:var(--y) translateX(var(--swipe-amount-x));opacity:1}to{transform:var(--y) translateX(calc(var(--swipe-amount-x) - 100%));opacity:0}}@keyframes swipe-out-right{from{transform:var(--y) translateX(var(--swipe-amount-x));opacity:1}to{transform:var(--y) translateX(calc(var(--swipe-amount-x) + 100%));opacity:0}}@keyframes swipe-out-up{from{transform:var(--y) translateY(var(--swipe-amount-y));opacity:1}to{transform:var(--y) translateY(calc(var(--swipe-amount-y) - 100%));opacity:0}}@keyframes swipe-out-down{from{transform:var(--y) translateY(var(--swipe-amount-y));opacity:1}to{transform:var(--y) translateY(calc(var(--swipe-amount-y) + 100%));opacity:0}}@media (max-width:600px){[data-sonner-toaster]{position:fixed;right:var(--mobile-offset-right);left:var(--mobile-offset-left);width:100%}[data-sonner-toaster][dir=rtl]{left:calc(var(--mobile-offset-left) * -1)}[data-sonner-toaster] [data-sonner-toast]{left:0;right:0;width:calc(100% - var(--mobile-offset-left) * 2)}[data-sonner-toaster][data-x-position=left]{left:var(--mobile-offset-left)}[data-sonner-toaster][data-y-position=bottom]{bottom:var(--mobile-offset-bottom)}[data-sonner-toaster][data-y-position=top]{top:var(--mobile-offset-top)}[data-sonner-toaster][data-x-position=center]{left:var(--mobile-offset-left);right:var(--mobile-offset-right);transform:none}}[data-sonner-toaster][data-sonner-theme=light]{--normal-bg:#fff;--normal-border:var(--gray4);--normal-text:var(--gray12);--success-bg:hsl(143, 85%, 96%);--success-border:hsl(145, 92%, 87%);--success-text:hsl(140, 100%, 27%);--info-bg:hsl(208, 100%, 97%);--info-border:hsl(221, 91%, 93%);--info-text:hsl(210, 92%, 45%);--warning-bg:hsl(49, 100%, 97%);--warning-border:hsl(49, 91%, 84%);--warning-text:hsl(31, 92%, 45%);--error-bg:hsl(359, 100%, 97%);--error-border:hsl(359, 100%, 94%);--error-text:hsl(360, 100%, 45%)}[data-sonner-toaster][data-sonner-theme=light] [data-sonner-toast][data-invert=true]{--normal-bg:#000;--normal-border:hsl(0, 0%, 20%);--normal-text:var(--gray1)}[data-sonner-toaster][data-sonner-theme=dark] [data-sonner-toast][data-invert=true]{--normal-bg:#fff;--normal-border:var(--gray3);--normal-text:var(--gray12)}[data-sonner-toaster][data-sonner-theme=dark]{--normal-bg:#000;--normal-bg-hover:hsl(0, 0%, 12%);--normal-border:hsl(0, 0%, 20%);--normal-border-hover:hsl(0, 0%, 25%);--normal-text:var(--gray1);--success-bg:hsl(150, 100%, 6%);--success-border:hsl(147, 100%, 12%);--success-text:hsl(150, 86%, 65%);--info-bg:hsl(215, 100%, 6%);--info-border:hsl(223, 43%, 17%);--info-text:hsl(216, 87%, 65%);--warning-bg:hsl(64, 100%, 6%);--warning-border:hsl(60, 100%, 9%);--warning-text:hsl(46, 87%, 65%);--error-bg:hsl(358, 76%, 10%);--error-border:hsl(357, 89%, 16%);--error-text:hsl(358, 100%, 81%)}[data-sonner-toaster][data-sonner-theme=dark] [data-sonner-toast] [data-close-button]{background:var(--normal-bg);border-color:var(--normal-border);color:var(--normal-text)}[data-sonner-toaster][data-sonner-theme=dark] [data-sonner-toast] [data-close-button]:hover{background:var(--normal-bg-hover);border-color:var(--normal-border-hover)}[data-rich-colors=true][data-sonner-toast][data-type=success]{background:var(--success-bg);border-color:var(--success-border);color:var(--success-text)}[data-rich-colors=true][data-sonner-toast][data-type=success] [data-close-button]{background:var(--success-bg);border-color:var(--success-border);color:var(--success-text)}[data-rich-colors=true][data-sonner-toast][data-type=info]{background:var(--info-bg);border-color:var(--info-border);color:var(--info-text)}[data-rich-colors=true][data-sonner-toast][data-type=info] [data-close-button]{background:var(--info-bg);border-color:var(--info-border);color:var(--info-text)}[data-rich-colors=true][data-sonner-toast][data-type=warning]{background:var(--warning-bg);border-color:var(--warning-border);color:var(--warning-text)}[data-rich-colors=true][data-sonner-toast][data-type=warning] [data-close-button]{background:var(--warning-bg);border-color:var(--warning-border);color:var(--warning-text)}[data-rich-colors=true][data-sonner-toast][data-type=error]{background:var(--error-bg);border-color:var(--error-border);color:var(--error-text)}[data-rich-colors=true][data-sonner-toast][data-type=error] [data-close-button]{background:var(--error-bg);border-color:var(--error-border);color:var(--error-text)}.sonner-loading-wrapper{--size:16px;height:var(--size);width:var(--size);position:absolute;inset:0;z-index:10}.sonner-loading-wrapper[data-visible=false]{transform-origin:center;animation:sonner-fade-out .2s ease forwards}.sonner-spinner{position:relative;top:50%;left:50%;height:var(--size);width:var(--size)}.sonner-loading-bar{animation:sonner-spin 1.2s linear infinite;background:var(--gray11);border-radius:6px;height:8%;left:-10%;position:absolute;top:-3.9%;width:24%}.sonner-loading-bar:first-child{animation-delay:-1.2s;transform:rotate(.0001deg) translate(146%)}.sonner-loading-bar:nth-child(2){animation-delay:-1.1s;transform:rotate(30deg) translate(146%)}.sonner-loading-bar:nth-child(3){animation-delay:-1s;transform:rotate(60deg) translate(146%)}.sonner-loading-bar:nth-child(4){animation-delay:-.9s;transform:rotate(90deg) translate(146%)}.sonner-loading-bar:nth-child(5){animation-delay:-.8s;transform:rotate(120deg) translate(146%)}.sonner-loading-bar:nth-child(6){animation-delay:-.7s;transform:rotate(150deg) translate(146%)}.sonner-loading-bar:nth-child(7){animation-delay:-.6s;transform:rotate(180deg) translate(146%)}.sonner-loading-bar:nth-child(8){animation-delay:-.5s;transform:rotate(210deg) translate(146%)}.sonner-loading-bar:nth-child(9){animation-delay:-.4s;transform:rotate(240deg) translate(146%)}.sonner-loading-bar:nth-child(10){animation-delay:-.3s;transform:rotate(270deg) translate(146%)}.sonner-loading-bar:nth-child(11){animation-delay:-.2s;transform:rotate(300deg) translate(146%)}.sonner-loading-bar:nth-child(12){animation-delay:-.1s;transform:rotate(330deg) translate(146%)}@keyframes sonner-fade-in{0%{opacity:0;transform:scale(.8)}100%{opacity:1;transform:scale(1)}}@keyframes sonner-fade-out{0%{opacity:1;transform:scale(1)}100%{opacity:0;transform:scale(.8)}}@keyframes sonner-spin{0%{opacity:1}100%{opacity:.15}}@media (prefers-reduced-motion){.sonner-loading-bar,[data-sonner-toast],[data-sonner-toast]>*{transition:none!important;animation:none!important}}.sonner-loader{position:absolute;top:50%;left:50%;transform:translate(-50%,-50%);transform-origin:center;transition:opacity .2s,transform .2s}.sonner-loader[data-visible=false]{opacity:0;transform:scale(.8) translate(-50%,-50%)}\");\nfunction isAction(action) {\n    return action.label !== undefined;\n}\n// Visible toasts amount\nconst VISIBLE_TOASTS_AMOUNT = 3;\n// Viewport padding\nconst VIEWPORT_OFFSET = '24px';\n// Mobile viewport padding\nconst MOBILE_VIEWPORT_OFFSET = '16px';\n// Default lifetime of a toasts (in ms)\nconst TOAST_LIFETIME = 4000;\n// Default toast width\nconst TOAST_WIDTH = 356;\n// Default gap between toasts\nconst GAP = 14;\n// Threshold to dismiss a toast\nconst SWIPE_THRESHOLD = 45;\n// Equal to exit animation duration\nconst TIME_BEFORE_UNMOUNT = 200;\nfunction cn() {\n    for(var _len = arguments.length, classes = new Array(_len), _key = 0; _key < _len; _key++){\n        classes[_key] = arguments[_key];\n    }\n    return classes.filter(Boolean).join(' ');\n}\nfunction getDefaultSwipeDirections(position) {\n    const [y, x] = position.split('-');\n    const directions = [];\n    if (y) {\n        directions.push(y);\n    }\n    if (x) {\n        directions.push(x);\n    }\n    return directions;\n}\nconst Toast = (props)=>{\n    _s1();\n    var _toast_classNames, _toast_classNames1, _toast_classNames2, _toast_classNames3, _toast_classNames4, _toast_classNames5, _toast_classNames6, _toast_classNames7, _toast_classNames8;\n    const { invert: ToasterInvert, toast, unstyled, interacting, setHeights, visibleToasts, heights, index, toasts, expanded, removeToast, defaultRichColors, closeButton: closeButtonFromToaster, style, cancelButtonStyle, actionButtonStyle, className = '', descriptionClassName = '', duration: durationFromToaster, position, gap, expandByDefault, classNames, icons, closeButtonAriaLabel = 'Close toast' } = props;\n    const [swipeDirection, setSwipeDirection] = react__WEBPACK_IMPORTED_MODULE_0__.useState(null);\n    const [swipeOutDirection, setSwipeOutDirection] = react__WEBPACK_IMPORTED_MODULE_0__.useState(null);\n    const [mounted, setMounted] = react__WEBPACK_IMPORTED_MODULE_0__.useState(false);\n    const [removed, setRemoved] = react__WEBPACK_IMPORTED_MODULE_0__.useState(false);\n    const [swiping, setSwiping] = react__WEBPACK_IMPORTED_MODULE_0__.useState(false);\n    const [swipeOut, setSwipeOut] = react__WEBPACK_IMPORTED_MODULE_0__.useState(false);\n    const [isSwiped, setIsSwiped] = react__WEBPACK_IMPORTED_MODULE_0__.useState(false);\n    const [offsetBeforeRemove, setOffsetBeforeRemove] = react__WEBPACK_IMPORTED_MODULE_0__.useState(0);\n    const [initialHeight, setInitialHeight] = react__WEBPACK_IMPORTED_MODULE_0__.useState(0);\n    const remainingTime = react__WEBPACK_IMPORTED_MODULE_0__.useRef(toast.duration || durationFromToaster || TOAST_LIFETIME);\n    const dragStartTime = react__WEBPACK_IMPORTED_MODULE_0__.useRef(null);\n    const toastRef = react__WEBPACK_IMPORTED_MODULE_0__.useRef(null);\n    const isFront = index === 0;\n    const isVisible = index + 1 <= visibleToasts;\n    const toastType = toast.type;\n    const dismissible = toast.dismissible !== false;\n    const toastClassname = toast.className || '';\n    const toastDescriptionClassname = toast.descriptionClassName || '';\n    // Height index is used to calculate the offset as it gets updated before the toast array, which means we can calculate the new layout faster.\n    const heightIndex = react__WEBPACK_IMPORTED_MODULE_0__.useMemo({\n        \"Toast.useMemo[heightIndex]\": ()=>heights.findIndex({\n                \"Toast.useMemo[heightIndex]\": (height)=>height.toastId === toast.id\n            }[\"Toast.useMemo[heightIndex]\"]) || 0\n    }[\"Toast.useMemo[heightIndex]\"], [\n        heights,\n        toast.id\n    ]);\n    const closeButton = react__WEBPACK_IMPORTED_MODULE_0__.useMemo({\n        \"Toast.useMemo[closeButton]\": ()=>{\n            var _toast_closeButton;\n            return (_toast_closeButton = toast.closeButton) != null ? _toast_closeButton : closeButtonFromToaster;\n        }\n    }[\"Toast.useMemo[closeButton]\"], [\n        toast.closeButton,\n        closeButtonFromToaster\n    ]);\n    const duration = react__WEBPACK_IMPORTED_MODULE_0__.useMemo({\n        \"Toast.useMemo[duration]\": ()=>toast.duration || durationFromToaster || TOAST_LIFETIME\n    }[\"Toast.useMemo[duration]\"], [\n        toast.duration,\n        durationFromToaster\n    ]);\n    const closeTimerStartTimeRef = react__WEBPACK_IMPORTED_MODULE_0__.useRef(0);\n    const offset = react__WEBPACK_IMPORTED_MODULE_0__.useRef(0);\n    const lastCloseTimerStartTimeRef = react__WEBPACK_IMPORTED_MODULE_0__.useRef(0);\n    const pointerStartRef = react__WEBPACK_IMPORTED_MODULE_0__.useRef(null);\n    const [y, x] = position.split('-');\n    const toastsHeightBefore = react__WEBPACK_IMPORTED_MODULE_0__.useMemo({\n        \"Toast.useMemo[toastsHeightBefore]\": ()=>{\n            return heights.reduce({\n                \"Toast.useMemo[toastsHeightBefore]\": (prev, curr, reducerIndex)=>{\n                    // Calculate offset up until current toast\n                    if (reducerIndex >= heightIndex) {\n                        return prev;\n                    }\n                    return prev + curr.height;\n                }\n            }[\"Toast.useMemo[toastsHeightBefore]\"], 0);\n        }\n    }[\"Toast.useMemo[toastsHeightBefore]\"], [\n        heights,\n        heightIndex\n    ]);\n    const isDocumentHidden = useIsDocumentHidden();\n    const invert = toast.invert || ToasterInvert;\n    const disabled = toastType === 'loading';\n    offset.current = react__WEBPACK_IMPORTED_MODULE_0__.useMemo({\n        \"Toast.useMemo\": ()=>heightIndex * gap + toastsHeightBefore\n    }[\"Toast.useMemo\"], [\n        heightIndex,\n        toastsHeightBefore\n    ]);\n    react__WEBPACK_IMPORTED_MODULE_0__.useEffect({\n        \"Toast.useEffect\": ()=>{\n            remainingTime.current = duration;\n        }\n    }[\"Toast.useEffect\"], [\n        duration\n    ]);\n    react__WEBPACK_IMPORTED_MODULE_0__.useEffect({\n        \"Toast.useEffect\": ()=>{\n            // Trigger enter animation without using CSS animation\n            setMounted(true);\n        }\n    }[\"Toast.useEffect\"], []);\n    react__WEBPACK_IMPORTED_MODULE_0__.useEffect({\n        \"Toast.useEffect\": ()=>{\n            const toastNode = toastRef.current;\n            if (toastNode) {\n                const height = toastNode.getBoundingClientRect().height;\n                // Add toast height to heights array after the toast is mounted\n                setInitialHeight(height);\n                setHeights({\n                    \"Toast.useEffect\": (h)=>[\n                            {\n                                toastId: toast.id,\n                                height,\n                                position: toast.position\n                            },\n                            ...h\n                        ]\n                }[\"Toast.useEffect\"]);\n                return ({\n                    \"Toast.useEffect\": ()=>setHeights({\n                            \"Toast.useEffect\": (h)=>h.filter({\n                                    \"Toast.useEffect\": (height)=>height.toastId !== toast.id\n                                }[\"Toast.useEffect\"])\n                        }[\"Toast.useEffect\"])\n                })[\"Toast.useEffect\"];\n            }\n        }\n    }[\"Toast.useEffect\"], [\n        setHeights,\n        toast.id\n    ]);\n    react__WEBPACK_IMPORTED_MODULE_0__.useLayoutEffect({\n        \"Toast.useLayoutEffect\": ()=>{\n            // Keep height up to date with the content in case it updates\n            if (!mounted) return;\n            const toastNode = toastRef.current;\n            const originalHeight = toastNode.style.height;\n            toastNode.style.height = 'auto';\n            const newHeight = toastNode.getBoundingClientRect().height;\n            toastNode.style.height = originalHeight;\n            setInitialHeight(newHeight);\n            setHeights({\n                \"Toast.useLayoutEffect\": (heights)=>{\n                    const alreadyExists = heights.find({\n                        \"Toast.useLayoutEffect.alreadyExists\": (height)=>height.toastId === toast.id\n                    }[\"Toast.useLayoutEffect.alreadyExists\"]);\n                    if (!alreadyExists) {\n                        return [\n                            {\n                                toastId: toast.id,\n                                height: newHeight,\n                                position: toast.position\n                            },\n                            ...heights\n                        ];\n                    } else {\n                        return heights.map({\n                            \"Toast.useLayoutEffect\": (height)=>height.toastId === toast.id ? {\n                                    ...height,\n                                    height: newHeight\n                                } : height\n                        }[\"Toast.useLayoutEffect\"]);\n                    }\n                }\n            }[\"Toast.useLayoutEffect\"]);\n        }\n    }[\"Toast.useLayoutEffect\"], [\n        mounted,\n        toast.title,\n        toast.description,\n        setHeights,\n        toast.id,\n        toast.jsx,\n        toast.action,\n        toast.cancel\n    ]);\n    const deleteToast = react__WEBPACK_IMPORTED_MODULE_0__.useCallback({\n        \"Toast.useCallback[deleteToast]\": ()=>{\n            // Save the offset for the exit swipe animation\n            setRemoved(true);\n            setOffsetBeforeRemove(offset.current);\n            setHeights({\n                \"Toast.useCallback[deleteToast]\": (h)=>h.filter({\n                        \"Toast.useCallback[deleteToast]\": (height)=>height.toastId !== toast.id\n                    }[\"Toast.useCallback[deleteToast]\"])\n            }[\"Toast.useCallback[deleteToast]\"]);\n            setTimeout({\n                \"Toast.useCallback[deleteToast]\": ()=>{\n                    removeToast(toast);\n                }\n            }[\"Toast.useCallback[deleteToast]\"], TIME_BEFORE_UNMOUNT);\n        }\n    }[\"Toast.useCallback[deleteToast]\"], [\n        toast,\n        removeToast,\n        setHeights,\n        offset\n    ]);\n    react__WEBPACK_IMPORTED_MODULE_0__.useEffect({\n        \"Toast.useEffect\": ()=>{\n            if (toast.promise && toastType === 'loading' || toast.duration === Infinity || toast.type === 'loading') return;\n            let timeoutId;\n            // Pause the timer on each hover\n            const pauseTimer = {\n                \"Toast.useEffect.pauseTimer\": ()=>{\n                    if (lastCloseTimerStartTimeRef.current < closeTimerStartTimeRef.current) {\n                        // Get the elapsed time since the timer started\n                        const elapsedTime = new Date().getTime() - closeTimerStartTimeRef.current;\n                        remainingTime.current = remainingTime.current - elapsedTime;\n                    }\n                    lastCloseTimerStartTimeRef.current = new Date().getTime();\n                }\n            }[\"Toast.useEffect.pauseTimer\"];\n            const startTimer = {\n                \"Toast.useEffect.startTimer\": ()=>{\n                    // setTimeout(, Infinity) behaves as if the delay is 0.\n                    // As a result, the toast would be closed immediately, giving the appearance that it was never rendered.\n                    // See: https://github.com/denysdovhan/wtfjs?tab=readme-ov-file#an-infinite-timeout\n                    if (remainingTime.current === Infinity) return;\n                    closeTimerStartTimeRef.current = new Date().getTime();\n                    // Let the toast know it has started\n                    timeoutId = setTimeout({\n                        \"Toast.useEffect.startTimer\": ()=>{\n                            toast.onAutoClose == null ? void 0 : toast.onAutoClose.call(toast, toast);\n                            deleteToast();\n                        }\n                    }[\"Toast.useEffect.startTimer\"], remainingTime.current);\n                }\n            }[\"Toast.useEffect.startTimer\"];\n            if (expanded || interacting || isDocumentHidden) {\n                pauseTimer();\n            } else {\n                startTimer();\n            }\n            return ({\n                \"Toast.useEffect\": ()=>clearTimeout(timeoutId)\n            })[\"Toast.useEffect\"];\n        }\n    }[\"Toast.useEffect\"], [\n        expanded,\n        interacting,\n        toast,\n        toastType,\n        isDocumentHidden,\n        deleteToast\n    ]);\n    react__WEBPACK_IMPORTED_MODULE_0__.useEffect({\n        \"Toast.useEffect\": ()=>{\n            if (toast.delete) {\n                deleteToast();\n                toast.onDismiss == null ? void 0 : toast.onDismiss.call(toast, toast);\n            }\n        }\n    }[\"Toast.useEffect\"], [\n        deleteToast,\n        toast.delete\n    ]);\n    function getLoadingIcon() {\n        var _toast_classNames;\n        if (icons == null ? void 0 : icons.loading) {\n            var _toast_classNames1;\n            return /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.createElement(\"div\", {\n                className: cn(classNames == null ? void 0 : classNames.loader, toast == null ? void 0 : (_toast_classNames1 = toast.classNames) == null ? void 0 : _toast_classNames1.loader, 'sonner-loader'),\n                \"data-visible\": toastType === 'loading'\n            }, icons.loading);\n        }\n        return /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.createElement(Loader, {\n            className: cn(classNames == null ? void 0 : classNames.loader, toast == null ? void 0 : (_toast_classNames = toast.classNames) == null ? void 0 : _toast_classNames.loader),\n            visible: toastType === 'loading'\n        });\n    }\n    const icon = toast.icon || (icons == null ? void 0 : icons[toastType]) || getAsset(toastType);\n    var _toast_richColors, _icons_close;\n    return /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.createElement(\"li\", {\n        tabIndex: 0,\n        ref: toastRef,\n        className: cn(className, toastClassname, classNames == null ? void 0 : classNames.toast, toast == null ? void 0 : (_toast_classNames = toast.classNames) == null ? void 0 : _toast_classNames.toast, classNames == null ? void 0 : classNames.default, classNames == null ? void 0 : classNames[toastType], toast == null ? void 0 : (_toast_classNames1 = toast.classNames) == null ? void 0 : _toast_classNames1[toastType]),\n        \"data-sonner-toast\": \"\",\n        \"data-rich-colors\": (_toast_richColors = toast.richColors) != null ? _toast_richColors : defaultRichColors,\n        \"data-styled\": !Boolean(toast.jsx || toast.unstyled || unstyled),\n        \"data-mounted\": mounted,\n        \"data-promise\": Boolean(toast.promise),\n        \"data-swiped\": isSwiped,\n        \"data-removed\": removed,\n        \"data-visible\": isVisible,\n        \"data-y-position\": y,\n        \"data-x-position\": x,\n        \"data-index\": index,\n        \"data-front\": isFront,\n        \"data-swiping\": swiping,\n        \"data-dismissible\": dismissible,\n        \"data-type\": toastType,\n        \"data-invert\": invert,\n        \"data-swipe-out\": swipeOut,\n        \"data-swipe-direction\": swipeOutDirection,\n        \"data-expanded\": Boolean(expanded || expandByDefault && mounted),\n        style: {\n            '--index': index,\n            '--toasts-before': index,\n            '--z-index': toasts.length - index,\n            '--offset': \"\".concat(removed ? offsetBeforeRemove : offset.current, \"px\"),\n            '--initial-height': expandByDefault ? 'auto' : \"\".concat(initialHeight, \"px\"),\n            ...style,\n            ...toast.style\n        },\n        onDragEnd: ()=>{\n            setSwiping(false);\n            setSwipeDirection(null);\n            pointerStartRef.current = null;\n        },\n        onPointerDown: (event)=>{\n            if (event.button === 2) return; // Return early on right click\n            if (disabled || !dismissible) return;\n            dragStartTime.current = new Date();\n            setOffsetBeforeRemove(offset.current);\n            // Ensure we maintain correct pointer capture even when going outside of the toast (e.g. when swiping)\n            event.target.setPointerCapture(event.pointerId);\n            if (event.target.tagName === 'BUTTON') return;\n            setSwiping(true);\n            pointerStartRef.current = {\n                x: event.clientX,\n                y: event.clientY\n            };\n        },\n        onPointerUp: ()=>{\n            var _toastRef_current, _toastRef_current1, _dragStartTime_current;\n            if (swipeOut || !dismissible) return;\n            pointerStartRef.current = null;\n            const swipeAmountX = Number(((_toastRef_current = toastRef.current) == null ? void 0 : _toastRef_current.style.getPropertyValue('--swipe-amount-x').replace('px', '')) || 0);\n            const swipeAmountY = Number(((_toastRef_current1 = toastRef.current) == null ? void 0 : _toastRef_current1.style.getPropertyValue('--swipe-amount-y').replace('px', '')) || 0);\n            const timeTaken = new Date().getTime() - ((_dragStartTime_current = dragStartTime.current) == null ? void 0 : _dragStartTime_current.getTime());\n            const swipeAmount = swipeDirection === 'x' ? swipeAmountX : swipeAmountY;\n            const velocity = Math.abs(swipeAmount) / timeTaken;\n            if (Math.abs(swipeAmount) >= SWIPE_THRESHOLD || velocity > 0.11) {\n                setOffsetBeforeRemove(offset.current);\n                toast.onDismiss == null ? void 0 : toast.onDismiss.call(toast, toast);\n                if (swipeDirection === 'x') {\n                    setSwipeOutDirection(swipeAmountX > 0 ? 'right' : 'left');\n                } else {\n                    setSwipeOutDirection(swipeAmountY > 0 ? 'down' : 'up');\n                }\n                deleteToast();\n                setSwipeOut(true);\n                return;\n            } else {\n                var _toastRef_current2, _toastRef_current3;\n                (_toastRef_current2 = toastRef.current) == null ? void 0 : _toastRef_current2.style.setProperty('--swipe-amount-x', \"0px\");\n                (_toastRef_current3 = toastRef.current) == null ? void 0 : _toastRef_current3.style.setProperty('--swipe-amount-y', \"0px\");\n            }\n            setIsSwiped(false);\n            setSwiping(false);\n            setSwipeDirection(null);\n        },\n        onPointerMove: (event)=>{\n            var _window_getSelection, _toastRef_current, _toastRef_current1;\n            if (!pointerStartRef.current || !dismissible) return;\n            const isHighlighted = ((_window_getSelection = window.getSelection()) == null ? void 0 : _window_getSelection.toString().length) > 0;\n            if (isHighlighted) return;\n            const yDelta = event.clientY - pointerStartRef.current.y;\n            const xDelta = event.clientX - pointerStartRef.current.x;\n            var _props_swipeDirections;\n            const swipeDirections = (_props_swipeDirections = props.swipeDirections) != null ? _props_swipeDirections : getDefaultSwipeDirections(position);\n            // Determine swipe direction if not already locked\n            if (!swipeDirection && (Math.abs(xDelta) > 1 || Math.abs(yDelta) > 1)) {\n                setSwipeDirection(Math.abs(xDelta) > Math.abs(yDelta) ? 'x' : 'y');\n            }\n            let swipeAmount = {\n                x: 0,\n                y: 0\n            };\n            const getDampening = (delta)=>{\n                const factor = Math.abs(delta) / 20;\n                return 1 / (1.5 + factor);\n            };\n            // Only apply swipe in the locked direction\n            if (swipeDirection === 'y') {\n                // Handle vertical swipes\n                if (swipeDirections.includes('top') || swipeDirections.includes('bottom')) {\n                    if (swipeDirections.includes('top') && yDelta < 0 || swipeDirections.includes('bottom') && yDelta > 0) {\n                        swipeAmount.y = yDelta;\n                    } else {\n                        // Smoothly transition to dampened movement\n                        const dampenedDelta = yDelta * getDampening(yDelta);\n                        // Ensure we don't jump when transitioning to dampened movement\n                        swipeAmount.y = Math.abs(dampenedDelta) < Math.abs(yDelta) ? dampenedDelta : yDelta;\n                    }\n                }\n            } else if (swipeDirection === 'x') {\n                // Handle horizontal swipes\n                if (swipeDirections.includes('left') || swipeDirections.includes('right')) {\n                    if (swipeDirections.includes('left') && xDelta < 0 || swipeDirections.includes('right') && xDelta > 0) {\n                        swipeAmount.x = xDelta;\n                    } else {\n                        // Smoothly transition to dampened movement\n                        const dampenedDelta = xDelta * getDampening(xDelta);\n                        // Ensure we don't jump when transitioning to dampened movement\n                        swipeAmount.x = Math.abs(dampenedDelta) < Math.abs(xDelta) ? dampenedDelta : xDelta;\n                    }\n                }\n            }\n            if (Math.abs(swipeAmount.x) > 0 || Math.abs(swipeAmount.y) > 0) {\n                setIsSwiped(true);\n            }\n            (_toastRef_current = toastRef.current) == null ? void 0 : _toastRef_current.style.setProperty('--swipe-amount-x', \"\".concat(swipeAmount.x, \"px\"));\n            (_toastRef_current1 = toastRef.current) == null ? void 0 : _toastRef_current1.style.setProperty('--swipe-amount-y', \"\".concat(swipeAmount.y, \"px\"));\n        }\n    }, closeButton && !toast.jsx && toastType !== 'loading' ? /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.createElement(\"button\", {\n        \"aria-label\": closeButtonAriaLabel,\n        \"data-disabled\": disabled,\n        \"data-close-button\": true,\n        onClick: disabled || !dismissible ? ()=>{} : ()=>{\n            deleteToast();\n            toast.onDismiss == null ? void 0 : toast.onDismiss.call(toast, toast);\n        },\n        className: cn(classNames == null ? void 0 : classNames.closeButton, toast == null ? void 0 : (_toast_classNames2 = toast.classNames) == null ? void 0 : _toast_classNames2.closeButton)\n    }, (_icons_close = icons == null ? void 0 : icons.close) != null ? _icons_close : CloseIcon) : null, (toastType || toast.icon || toast.promise) && toast.icon !== null && ((icons == null ? void 0 : icons[toastType]) !== null || toast.icon) ? /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.createElement(\"div\", {\n        \"data-icon\": \"\",\n        className: cn(classNames == null ? void 0 : classNames.icon, toast == null ? void 0 : (_toast_classNames3 = toast.classNames) == null ? void 0 : _toast_classNames3.icon)\n    }, toast.promise || toast.type === 'loading' && !toast.icon ? toast.icon || getLoadingIcon() : null, toast.type !== 'loading' ? icon : null) : null, /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.createElement(\"div\", {\n        \"data-content\": \"\",\n        className: cn(classNames == null ? void 0 : classNames.content, toast == null ? void 0 : (_toast_classNames4 = toast.classNames) == null ? void 0 : _toast_classNames4.content)\n    }, /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.createElement(\"div\", {\n        \"data-title\": \"\",\n        className: cn(classNames == null ? void 0 : classNames.title, toast == null ? void 0 : (_toast_classNames5 = toast.classNames) == null ? void 0 : _toast_classNames5.title)\n    }, toast.jsx ? toast.jsx : typeof toast.title === 'function' ? toast.title() : toast.title), toast.description ? /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.createElement(\"div\", {\n        \"data-description\": \"\",\n        className: cn(descriptionClassName, toastDescriptionClassname, classNames == null ? void 0 : classNames.description, toast == null ? void 0 : (_toast_classNames6 = toast.classNames) == null ? void 0 : _toast_classNames6.description)\n    }, typeof toast.description === 'function' ? toast.description() : toast.description) : null), /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.isValidElement(toast.cancel) ? toast.cancel : toast.cancel && isAction(toast.cancel) ? /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.createElement(\"button\", {\n        \"data-button\": true,\n        \"data-cancel\": true,\n        style: toast.cancelButtonStyle || cancelButtonStyle,\n        onClick: (event)=>{\n            // We need to check twice because typescript\n            if (!isAction(toast.cancel)) return;\n            if (!dismissible) return;\n            toast.cancel.onClick == null ? void 0 : toast.cancel.onClick.call(toast.cancel, event);\n            deleteToast();\n        },\n        className: cn(classNames == null ? void 0 : classNames.cancelButton, toast == null ? void 0 : (_toast_classNames7 = toast.classNames) == null ? void 0 : _toast_classNames7.cancelButton)\n    }, toast.cancel.label) : null, /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.isValidElement(toast.action) ? toast.action : toast.action && isAction(toast.action) ? /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.createElement(\"button\", {\n        \"data-button\": true,\n        \"data-action\": true,\n        style: toast.actionButtonStyle || actionButtonStyle,\n        onClick: (event)=>{\n            // We need to check twice because typescript\n            if (!isAction(toast.action)) return;\n            toast.action.onClick == null ? void 0 : toast.action.onClick.call(toast.action, event);\n            if (event.defaultPrevented) return;\n            deleteToast();\n        },\n        className: cn(classNames == null ? void 0 : classNames.actionButton, toast == null ? void 0 : (_toast_classNames8 = toast.classNames) == null ? void 0 : _toast_classNames8.actionButton)\n    }, toast.action.label) : null);\n};\n_s1(Toast, \"Hs2RwklMUctKsF2fEbXUzesmn3w=\", false, function() {\n    return [\n        useIsDocumentHidden\n    ];\n});\n_c1 = Toast;\nfunction getDocumentDirection() {\n    if (typeof window === 'undefined') return 'ltr';\n    if (typeof document === 'undefined') return 'ltr'; // For Fresh purpose\n    const dirAttribute = document.documentElement.getAttribute('dir');\n    if (dirAttribute === 'auto' || !dirAttribute) {\n        return window.getComputedStyle(document.documentElement).direction;\n    }\n    return dirAttribute;\n}\nfunction assignOffset(defaultOffset, mobileOffset) {\n    const styles = {};\n    [\n        defaultOffset,\n        mobileOffset\n    ].forEach((offset, index)=>{\n        const isMobile = index === 1;\n        const prefix = isMobile ? '--mobile-offset' : '--offset';\n        const defaultValue = isMobile ? MOBILE_VIEWPORT_OFFSET : VIEWPORT_OFFSET;\n        function assignAll(offset) {\n            [\n                'top',\n                'right',\n                'bottom',\n                'left'\n            ].forEach((key)=>{\n                styles[\"\".concat(prefix, \"-\").concat(key)] = typeof offset === 'number' ? \"\".concat(offset, \"px\") : offset;\n            });\n        }\n        if (typeof offset === 'number' || typeof offset === 'string') {\n            assignAll(offset);\n        } else if (typeof offset === 'object') {\n            [\n                'top',\n                'right',\n                'bottom',\n                'left'\n            ].forEach((key)=>{\n                if (offset[key] === undefined) {\n                    styles[\"\".concat(prefix, \"-\").concat(key)] = defaultValue;\n                } else {\n                    styles[\"\".concat(prefix, \"-\").concat(key)] = typeof offset[key] === 'number' ? \"\".concat(offset[key], \"px\") : offset[key];\n                }\n            });\n        } else {\n            assignAll(defaultValue);\n        }\n    });\n    return styles;\n}\nfunction useSonner() {\n    _s2();\n    const [activeToasts, setActiveToasts] = react__WEBPACK_IMPORTED_MODULE_0__.useState([]);\n    react__WEBPACK_IMPORTED_MODULE_0__.useEffect({\n        \"useSonner.useEffect\": ()=>{\n            return ToastState.subscribe({\n                \"useSonner.useEffect\": (toast)=>{\n                    if (toast.dismiss) {\n                        setTimeout({\n                            \"useSonner.useEffect\": ()=>{\n                                react_dom__WEBPACK_IMPORTED_MODULE_1__.flushSync({\n                                    \"useSonner.useEffect\": ()=>{\n                                        setActiveToasts({\n                                            \"useSonner.useEffect\": (toasts)=>toasts.filter({\n                                                    \"useSonner.useEffect\": (t)=>t.id !== toast.id\n                                                }[\"useSonner.useEffect\"])\n                                        }[\"useSonner.useEffect\"]);\n                                    }\n                                }[\"useSonner.useEffect\"]);\n                            }\n                        }[\"useSonner.useEffect\"]);\n                        return;\n                    }\n                    // Prevent batching, temp solution.\n                    setTimeout({\n                        \"useSonner.useEffect\": ()=>{\n                            react_dom__WEBPACK_IMPORTED_MODULE_1__.flushSync({\n                                \"useSonner.useEffect\": ()=>{\n                                    setActiveToasts({\n                                        \"useSonner.useEffect\": (toasts)=>{\n                                            const indexOfExistingToast = toasts.findIndex({\n                                                \"useSonner.useEffect.indexOfExistingToast\": (t)=>t.id === toast.id\n                                            }[\"useSonner.useEffect.indexOfExistingToast\"]);\n                                            // Update the toast if it already exists\n                                            if (indexOfExistingToast !== -1) {\n                                                return [\n                                                    ...toasts.slice(0, indexOfExistingToast),\n                                                    {\n                                                        ...toasts[indexOfExistingToast],\n                                                        ...toast\n                                                    },\n                                                    ...toasts.slice(indexOfExistingToast + 1)\n                                                ];\n                                            }\n                                            return [\n                                                toast,\n                                                ...toasts\n                                            ];\n                                        }\n                                    }[\"useSonner.useEffect\"]);\n                                }\n                            }[\"useSonner.useEffect\"]);\n                        }\n                    }[\"useSonner.useEffect\"]);\n                }\n            }[\"useSonner.useEffect\"]);\n        }\n    }[\"useSonner.useEffect\"], []);\n    return {\n        toasts: activeToasts\n    };\n}\n_s2(useSonner, \"wvKkrpl8d9UBJsfUcWYgFEOa7SA=\");\nconst Toaster = /*#__PURE__*/ _s3(react__WEBPACK_IMPORTED_MODULE_0__.forwardRef(_c2 = _s3(function Toaster(props, ref) {\n    _s3();\n    const { invert, position = 'bottom-right', hotkey = [\n        'altKey',\n        'KeyT'\n    ], expand, closeButton, className, offset, mobileOffset, theme = 'light', richColors, duration, style, visibleToasts = VISIBLE_TOASTS_AMOUNT, toastOptions, dir = getDocumentDirection(), gap = GAP, icons, containerAriaLabel = 'Notifications' } = props;\n    const [toasts, setToasts] = react__WEBPACK_IMPORTED_MODULE_0__.useState([]);\n    const possiblePositions = react__WEBPACK_IMPORTED_MODULE_0__.useMemo({\n        \"Toaster.Toaster.useMemo[possiblePositions]\": ()=>{\n            return Array.from(new Set([\n                position\n            ].concat(toasts.filter({\n                \"Toaster.Toaster.useMemo[possiblePositions]\": (toast)=>toast.position\n            }[\"Toaster.Toaster.useMemo[possiblePositions]\"]).map({\n                \"Toaster.Toaster.useMemo[possiblePositions]\": (toast)=>toast.position\n            }[\"Toaster.Toaster.useMemo[possiblePositions]\"]))));\n        }\n    }[\"Toaster.Toaster.useMemo[possiblePositions]\"], [\n        toasts,\n        position\n    ]);\n    const [heights, setHeights] = react__WEBPACK_IMPORTED_MODULE_0__.useState([]);\n    const [expanded, setExpanded] = react__WEBPACK_IMPORTED_MODULE_0__.useState(false);\n    const [interacting, setInteracting] = react__WEBPACK_IMPORTED_MODULE_0__.useState(false);\n    const [actualTheme, setActualTheme] = react__WEBPACK_IMPORTED_MODULE_0__.useState(theme !== 'system' ? theme : typeof window !== 'undefined' ? window.matchMedia && window.matchMedia('(prefers-color-scheme: dark)').matches ? 'dark' : 'light' : 'light');\n    const listRef = react__WEBPACK_IMPORTED_MODULE_0__.useRef(null);\n    const hotkeyLabel = hotkey.join('+').replace(/Key/g, '').replace(/Digit/g, '');\n    const lastFocusedElementRef = react__WEBPACK_IMPORTED_MODULE_0__.useRef(null);\n    const isFocusWithinRef = react__WEBPACK_IMPORTED_MODULE_0__.useRef(false);\n    const removeToast = react__WEBPACK_IMPORTED_MODULE_0__.useCallback({\n        \"Toaster.Toaster.useCallback[removeToast]\": (toastToRemove)=>{\n            setToasts({\n                \"Toaster.Toaster.useCallback[removeToast]\": (toasts)=>{\n                    var _toasts_find;\n                    if (!((_toasts_find = toasts.find({\n                        \"Toaster.Toaster.useCallback[removeToast]\": (toast)=>toast.id === toastToRemove.id\n                    }[\"Toaster.Toaster.useCallback[removeToast]\"])) == null ? void 0 : _toasts_find.delete)) {\n                        ToastState.dismiss(toastToRemove.id);\n                    }\n                    return toasts.filter({\n                        \"Toaster.Toaster.useCallback[removeToast]\": (param)=>{\n                            let { id } = param;\n                            return id !== toastToRemove.id;\n                        }\n                    }[\"Toaster.Toaster.useCallback[removeToast]\"]);\n                }\n            }[\"Toaster.Toaster.useCallback[removeToast]\"]);\n        }\n    }[\"Toaster.Toaster.useCallback[removeToast]\"], []);\n    react__WEBPACK_IMPORTED_MODULE_0__.useEffect({\n        \"Toaster.Toaster.useEffect\": ()=>{\n            return ToastState.subscribe({\n                \"Toaster.Toaster.useEffect\": (toast)=>{\n                    if (toast.dismiss) {\n                        // Prevent batching of other state updates\n                        requestAnimationFrame({\n                            \"Toaster.Toaster.useEffect\": ()=>{\n                                setToasts({\n                                    \"Toaster.Toaster.useEffect\": (toasts)=>toasts.map({\n                                            \"Toaster.Toaster.useEffect\": (t)=>t.id === toast.id ? {\n                                                    ...t,\n                                                    delete: true\n                                                } : t\n                                        }[\"Toaster.Toaster.useEffect\"])\n                                }[\"Toaster.Toaster.useEffect\"]);\n                            }\n                        }[\"Toaster.Toaster.useEffect\"]);\n                        return;\n                    }\n                    // Prevent batching, temp solution.\n                    setTimeout({\n                        \"Toaster.Toaster.useEffect\": ()=>{\n                            react_dom__WEBPACK_IMPORTED_MODULE_1__.flushSync({\n                                \"Toaster.Toaster.useEffect\": ()=>{\n                                    setToasts({\n                                        \"Toaster.Toaster.useEffect\": (toasts)=>{\n                                            const indexOfExistingToast = toasts.findIndex({\n                                                \"Toaster.Toaster.useEffect.indexOfExistingToast\": (t)=>t.id === toast.id\n                                            }[\"Toaster.Toaster.useEffect.indexOfExistingToast\"]);\n                                            // Update the toast if it already exists\n                                            if (indexOfExistingToast !== -1) {\n                                                return [\n                                                    ...toasts.slice(0, indexOfExistingToast),\n                                                    {\n                                                        ...toasts[indexOfExistingToast],\n                                                        ...toast\n                                                    },\n                                                    ...toasts.slice(indexOfExistingToast + 1)\n                                                ];\n                                            }\n                                            return [\n                                                toast,\n                                                ...toasts\n                                            ];\n                                        }\n                                    }[\"Toaster.Toaster.useEffect\"]);\n                                }\n                            }[\"Toaster.Toaster.useEffect\"]);\n                        }\n                    }[\"Toaster.Toaster.useEffect\"]);\n                }\n            }[\"Toaster.Toaster.useEffect\"]);\n        }\n    }[\"Toaster.Toaster.useEffect\"], [\n        toasts\n    ]);\n    react__WEBPACK_IMPORTED_MODULE_0__.useEffect({\n        \"Toaster.Toaster.useEffect\": ()=>{\n            if (theme !== 'system') {\n                setActualTheme(theme);\n                return;\n            }\n            if (theme === 'system') {\n                // check if current preference is dark\n                if (window.matchMedia && window.matchMedia('(prefers-color-scheme: dark)').matches) {\n                    // it's currently dark\n                    setActualTheme('dark');\n                } else {\n                    // it's not dark\n                    setActualTheme('light');\n                }\n            }\n            if (typeof window === 'undefined') return;\n            const darkMediaQuery = window.matchMedia('(prefers-color-scheme: dark)');\n            try {\n                // Chrome & Firefox\n                darkMediaQuery.addEventListener('change', {\n                    \"Toaster.Toaster.useEffect\": (param)=>{\n                        let { matches } = param;\n                        if (matches) {\n                            setActualTheme('dark');\n                        } else {\n                            setActualTheme('light');\n                        }\n                    }\n                }[\"Toaster.Toaster.useEffect\"]);\n            } catch (error) {\n                // Safari < 14\n                darkMediaQuery.addListener({\n                    \"Toaster.Toaster.useEffect\": (param)=>{\n                        let { matches } = param;\n                        try {\n                            if (matches) {\n                                setActualTheme('dark');\n                            } else {\n                                setActualTheme('light');\n                            }\n                        } catch (e) {\n                            console.error(e);\n                        }\n                    }\n                }[\"Toaster.Toaster.useEffect\"]);\n            }\n        }\n    }[\"Toaster.Toaster.useEffect\"], [\n        theme\n    ]);\n    react__WEBPACK_IMPORTED_MODULE_0__.useEffect({\n        \"Toaster.Toaster.useEffect\": ()=>{\n            // Ensure expanded is always false when no toasts are present / only one left\n            if (toasts.length <= 1) {\n                setExpanded(false);\n            }\n        }\n    }[\"Toaster.Toaster.useEffect\"], [\n        toasts\n    ]);\n    react__WEBPACK_IMPORTED_MODULE_0__.useEffect({\n        \"Toaster.Toaster.useEffect\": ()=>{\n            const handleKeyDown = {\n                \"Toaster.Toaster.useEffect.handleKeyDown\": (event)=>{\n                    var _listRef_current;\n                    const isHotkeyPressed = hotkey.every({\n                        \"Toaster.Toaster.useEffect.handleKeyDown.isHotkeyPressed\": (key)=>event[key] || event.code === key\n                    }[\"Toaster.Toaster.useEffect.handleKeyDown.isHotkeyPressed\"]);\n                    if (isHotkeyPressed) {\n                        var _listRef_current1;\n                        setExpanded(true);\n                        (_listRef_current1 = listRef.current) == null ? void 0 : _listRef_current1.focus();\n                    }\n                    if (event.code === 'Escape' && (document.activeElement === listRef.current || ((_listRef_current = listRef.current) == null ? void 0 : _listRef_current.contains(document.activeElement)))) {\n                        setExpanded(false);\n                    }\n                }\n            }[\"Toaster.Toaster.useEffect.handleKeyDown\"];\n            document.addEventListener('keydown', handleKeyDown);\n            return ({\n                \"Toaster.Toaster.useEffect\": ()=>document.removeEventListener('keydown', handleKeyDown)\n            })[\"Toaster.Toaster.useEffect\"];\n        }\n    }[\"Toaster.Toaster.useEffect\"], [\n        hotkey\n    ]);\n    react__WEBPACK_IMPORTED_MODULE_0__.useEffect({\n        \"Toaster.Toaster.useEffect\": ()=>{\n            if (listRef.current) {\n                return ({\n                    \"Toaster.Toaster.useEffect\": ()=>{\n                        if (lastFocusedElementRef.current) {\n                            lastFocusedElementRef.current.focus({\n                                preventScroll: true\n                            });\n                            lastFocusedElementRef.current = null;\n                            isFocusWithinRef.current = false;\n                        }\n                    }\n                })[\"Toaster.Toaster.useEffect\"];\n            }\n        }\n    }[\"Toaster.Toaster.useEffect\"], [\n        listRef.current\n    ]);\n    return /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.createElement(\"section\", {\n        ref: ref,\n        \"aria-label\": \"\".concat(containerAriaLabel, \" \").concat(hotkeyLabel),\n        tabIndex: -1,\n        \"aria-live\": \"polite\",\n        \"aria-relevant\": \"additions text\",\n        \"aria-atomic\": \"false\",\n        suppressHydrationWarning: true\n    }, possiblePositions.map((position, index)=>{\n        var _heights_;\n        const [y, x] = position.split('-');\n        if (!toasts.length) return null;\n        return /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.createElement(\"ol\", {\n            key: position,\n            dir: dir === 'auto' ? getDocumentDirection() : dir,\n            tabIndex: -1,\n            ref: listRef,\n            className: className,\n            \"data-sonner-toaster\": true,\n            \"data-sonner-theme\": actualTheme,\n            \"data-y-position\": y,\n            \"data-x-position\": x,\n            style: {\n                '--front-toast-height': \"\".concat(((_heights_ = heights[0]) == null ? void 0 : _heights_.height) || 0, \"px\"),\n                '--width': \"\".concat(TOAST_WIDTH, \"px\"),\n                '--gap': \"\".concat(gap, \"px\"),\n                ...style,\n                ...assignOffset(offset, mobileOffset)\n            },\n            onBlur: (event)=>{\n                if (isFocusWithinRef.current && !event.currentTarget.contains(event.relatedTarget)) {\n                    isFocusWithinRef.current = false;\n                    if (lastFocusedElementRef.current) {\n                        lastFocusedElementRef.current.focus({\n                            preventScroll: true\n                        });\n                        lastFocusedElementRef.current = null;\n                    }\n                }\n            },\n            onFocus: (event)=>{\n                const isNotDismissible = event.target instanceof HTMLElement && event.target.dataset.dismissible === 'false';\n                if (isNotDismissible) return;\n                if (!isFocusWithinRef.current) {\n                    isFocusWithinRef.current = true;\n                    lastFocusedElementRef.current = event.relatedTarget;\n                }\n            },\n            onMouseEnter: ()=>setExpanded(true),\n            onMouseMove: ()=>setExpanded(true),\n            onMouseLeave: ()=>{\n                // Avoid setting expanded to false when interacting with a toast, e.g. swiping\n                if (!interacting) {\n                    setExpanded(false);\n                }\n            },\n            onDragEnd: ()=>setExpanded(false),\n            onPointerDown: (event)=>{\n                const isNotDismissible = event.target instanceof HTMLElement && event.target.dataset.dismissible === 'false';\n                if (isNotDismissible) return;\n                setInteracting(true);\n            },\n            onPointerUp: ()=>setInteracting(false)\n        }, toasts.filter((toast)=>!toast.position && index === 0 || toast.position === position).map((toast, index)=>{\n            var _toastOptions_duration, _toastOptions_closeButton;\n            return /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.createElement(Toast, {\n                key: toast.id,\n                icons: icons,\n                index: index,\n                toast: toast,\n                defaultRichColors: richColors,\n                duration: (_toastOptions_duration = toastOptions == null ? void 0 : toastOptions.duration) != null ? _toastOptions_duration : duration,\n                className: toastOptions == null ? void 0 : toastOptions.className,\n                descriptionClassName: toastOptions == null ? void 0 : toastOptions.descriptionClassName,\n                invert: invert,\n                visibleToasts: visibleToasts,\n                closeButton: (_toastOptions_closeButton = toastOptions == null ? void 0 : toastOptions.closeButton) != null ? _toastOptions_closeButton : closeButton,\n                interacting: interacting,\n                position: position,\n                style: toastOptions == null ? void 0 : toastOptions.style,\n                unstyled: toastOptions == null ? void 0 : toastOptions.unstyled,\n                classNames: toastOptions == null ? void 0 : toastOptions.classNames,\n                cancelButtonStyle: toastOptions == null ? void 0 : toastOptions.cancelButtonStyle,\n                actionButtonStyle: toastOptions == null ? void 0 : toastOptions.actionButtonStyle,\n                closeButtonAriaLabel: toastOptions == null ? void 0 : toastOptions.closeButtonAriaLabel,\n                removeToast: removeToast,\n                toasts: toasts.filter((t)=>t.position == toast.position),\n                heights: heights.filter((h)=>h.position == toast.position),\n                setHeights: setHeights,\n                expandByDefault: expand,\n                gap: gap,\n                expanded: expanded,\n                swipeDirections: props.swipeDirections\n            });\n        }));\n    }));\n}, \"oqEGKFhGV9uIBJI/pmW6D0z1xPo=\")), \"oqEGKFhGV9uIBJI/pmW6D0z1xPo=\");\n_c3 = Toaster;\n\nvar _c, _c1, _c2, _c3;\n$RefreshReg$(_c, \"Loader\");\n$RefreshReg$(_c1, \"Toast\");\n$RefreshReg$(_c2, \"Toaster$React.forwardRef\");\n$RefreshReg$(_c3, \"Toaster\");\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/sonner/dist/index.mjs\n"));

/***/ }),

/***/ "(app-pages-browser)/./src/app/globals.css":
/*!*****************************!*\
  !*** ./src/app/globals.css ***!
  \*****************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (\"e7f447f8e79f\");\nif (true) { module.hot.accept() }\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL3NyYy9hcHAvZ2xvYmFscy5jc3MiLCJtYXBwaW5ncyI6Ijs7OztBQUFBLGlFQUFlLGNBQWM7QUFDN0IsSUFBSSxJQUFVLElBQUksaUJBQWlCIiwic291cmNlcyI6WyJDOlxcVXNlcnNcXFBDXFxEb3dubG9hZHNcXE5ldyBmb2xkZXIgKDIyKVxcdmlyYWwtY2xpcC1nZW5lcmF0b3JcXHNyY1xcYXBwXFxnbG9iYWxzLmNzcyJdLCJzb3VyY2VzQ29udGVudCI6WyJleHBvcnQgZGVmYXVsdCBcImU3ZjQ0N2Y4ZTc5ZlwiXG5pZiAobW9kdWxlLmhvdCkgeyBtb2R1bGUuaG90LmFjY2VwdCgpIH1cbiJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/globals.css\n"));

/***/ }),

/***/ "(app-pages-browser)/./src/components/theme-provider.tsx":
/*!*******************************************!*\
  !*** ./src/components/theme-provider.tsx ***!
  \*******************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   ThemeProvider: () => (/* binding */ ThemeProvider)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_themes__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next-themes */ \"(app-pages-browser)/./node_modules/next-themes/dist/index.mjs\");\n/* __next_internal_client_entry_do_not_use__ ThemeProvider auto */ \n\n\nfunction ThemeProvider(param) {\n    let { children, ...props } = param;\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_themes__WEBPACK_IMPORTED_MODULE_2__.ThemeProvider, {\n        ...props,\n        children: children\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\New folder (22)\\\\viral-clip-generator\\\\src\\\\components\\\\theme-provider.tsx\",\n        lineNumber: 8,\n        columnNumber: 10\n    }, this);\n}\n_c = ThemeProvider;\nvar _c;\n$RefreshReg$(_c, \"ThemeProvider\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL3NyYy9jb21wb25lbnRzL3RoZW1lLXByb3ZpZGVyLnRzeCIsIm1hcHBpbmdzIjoiOzs7Ozs7Ozs7QUFFOEI7QUFDbUM7QUFHMUQsU0FBU0MsY0FBYyxLQUEwQztRQUExQyxFQUFFRSxRQUFRLEVBQUUsR0FBR0MsT0FBMkIsR0FBMUM7SUFDNUIscUJBQU8sOERBQUNGLHNEQUFrQkE7UUFBRSxHQUFHRSxLQUFLO2tCQUFHRDs7Ozs7O0FBQ3pDO0tBRmdCRiIsInNvdXJjZXMiOlsiQzpcXFVzZXJzXFxQQ1xcRG93bmxvYWRzXFxOZXcgZm9sZGVyICgyMilcXHZpcmFsLWNsaXAtZ2VuZXJhdG9yXFxzcmNcXGNvbXBvbmVudHNcXHRoZW1lLXByb3ZpZGVyLnRzeCJdLCJzb3VyY2VzQ29udGVudCI6WyJcInVzZSBjbGllbnRcIlxuXG5pbXBvcnQgKiBhcyBSZWFjdCBmcm9tIFwicmVhY3RcIlxuaW1wb3J0IHsgVGhlbWVQcm92aWRlciBhcyBOZXh0VGhlbWVzUHJvdmlkZXIgfSBmcm9tIFwibmV4dC10aGVtZXNcIlxuaW1wb3J0IHsgdHlwZSBUaGVtZVByb3ZpZGVyUHJvcHMgfSBmcm9tIFwibmV4dC10aGVtZXMvZGlzdC90eXBlc1wiXG5cbmV4cG9ydCBmdW5jdGlvbiBUaGVtZVByb3ZpZGVyKHsgY2hpbGRyZW4sIC4uLnByb3BzIH06IFRoZW1lUHJvdmlkZXJQcm9wcykge1xuICByZXR1cm4gPE5leHRUaGVtZXNQcm92aWRlciB7Li4ucHJvcHN9PntjaGlsZHJlbn08L05leHRUaGVtZXNQcm92aWRlcj5cbn1cbiJdLCJuYW1lcyI6WyJSZWFjdCIsIlRoZW1lUHJvdmlkZXIiLCJOZXh0VGhlbWVzUHJvdmlkZXIiLCJjaGlsZHJlbiIsInByb3BzIl0sImlnbm9yZUxpc3QiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/theme-provider.tsx\n"));

/***/ }),

/***/ "(app-pages-browser)/./src/components/ui/sonner.tsx":
/*!**************************************!*\
  !*** ./src/components/ui/sonner.tsx ***!
  \**************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Toaster: () => (/* binding */ Toaster)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var next_themes__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next-themes */ \"(app-pages-browser)/./node_modules/next-themes/dist/index.mjs\");\n/* harmony import */ var sonner__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! sonner */ \"(app-pages-browser)/./node_modules/sonner/dist/index.mjs\");\n/* __next_internal_client_entry_do_not_use__ Toaster auto */ \nvar _s = $RefreshSig$();\n\n\nconst Toaster = (param)=>{\n    let { ...props } = param;\n    _s();\n    const { theme = \"system\" } = (0,next_themes__WEBPACK_IMPORTED_MODULE_1__.useTheme)();\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(sonner__WEBPACK_IMPORTED_MODULE_2__.Toaster, {\n        theme: theme,\n        className: \"toaster group\",\n        style: {\n            \"--normal-bg\": \"var(--popover)\",\n            \"--normal-text\": \"var(--popover-foreground)\",\n            \"--normal-border\": \"var(--border)\"\n        },\n        ...props\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\New folder (22)\\\\viral-clip-generator\\\\src\\\\components\\\\ui\\\\sonner.tsx\",\n        lineNumber: 10,\n        columnNumber: 5\n    }, undefined);\n};\n_s(Toaster, \"EriOrahfenYKDCErPq+L6926Dw4=\", false, function() {\n    return [\n        next_themes__WEBPACK_IMPORTED_MODULE_1__.useTheme\n    ];\n});\n_c = Toaster;\n\nvar _c;\n$RefreshReg$(_c, \"Toaster\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL3NyYy9jb21wb25lbnRzL3VpL3Nvbm5lci50c3giLCJtYXBwaW5ncyI6Ijs7Ozs7Ozs7O0FBRXNDO0FBQ2tCO0FBRXhELE1BQU1DLFVBQVU7UUFBQyxFQUFFLEdBQUdFLE9BQXFCOztJQUN6QyxNQUFNLEVBQUVDLFFBQVEsUUFBUSxFQUFFLEdBQUdKLHFEQUFRQTtJQUVyQyxxQkFDRSw4REFBQ0UsMkNBQU1BO1FBQ0xFLE9BQU9BO1FBQ1BDLFdBQVU7UUFDVkMsT0FDRTtZQUNFLGVBQWU7WUFDZixpQkFBaUI7WUFDakIsbUJBQW1CO1FBQ3JCO1FBRUQsR0FBR0gsS0FBSzs7Ozs7O0FBR2Y7R0FqQk1GOztRQUN5QkQsaURBQVFBOzs7S0FEakNDO0FBbUJZIiwic291cmNlcyI6WyJDOlxcVXNlcnNcXFBDXFxEb3dubG9hZHNcXE5ldyBmb2xkZXIgKDIyKVxcdmlyYWwtY2xpcC1nZW5lcmF0b3JcXHNyY1xcY29tcG9uZW50c1xcdWlcXHNvbm5lci50c3giXSwic291cmNlc0NvbnRlbnQiOlsiXCJ1c2UgY2xpZW50XCJcblxuaW1wb3J0IHsgdXNlVGhlbWUgfSBmcm9tIFwibmV4dC10aGVtZXNcIlxuaW1wb3J0IHsgVG9hc3RlciBhcyBTb25uZXIsIFRvYXN0ZXJQcm9wcyB9IGZyb20gXCJzb25uZXJcIlxuXG5jb25zdCBUb2FzdGVyID0gKHsgLi4ucHJvcHMgfTogVG9hc3RlclByb3BzKSA9PiB7XG4gIGNvbnN0IHsgdGhlbWUgPSBcInN5c3RlbVwiIH0gPSB1c2VUaGVtZSgpXG5cbiAgcmV0dXJuIChcbiAgICA8U29ubmVyXG4gICAgICB0aGVtZT17dGhlbWUgYXMgVG9hc3RlclByb3BzW1widGhlbWVcIl19XG4gICAgICBjbGFzc05hbWU9XCJ0b2FzdGVyIGdyb3VwXCJcbiAgICAgIHN0eWxlPXtcbiAgICAgICAge1xuICAgICAgICAgIFwiLS1ub3JtYWwtYmdcIjogXCJ2YXIoLS1wb3BvdmVyKVwiLFxuICAgICAgICAgIFwiLS1ub3JtYWwtdGV4dFwiOiBcInZhcigtLXBvcG92ZXItZm9yZWdyb3VuZClcIixcbiAgICAgICAgICBcIi0tbm9ybWFsLWJvcmRlclwiOiBcInZhcigtLWJvcmRlcilcIixcbiAgICAgICAgfSBhcyBSZWFjdC5DU1NQcm9wZXJ0aWVzXG4gICAgICB9XG4gICAgICB7Li4ucHJvcHN9XG4gICAgLz5cbiAgKVxufVxuXG5leHBvcnQgeyBUb2FzdGVyIH1cbiJdLCJuYW1lcyI6WyJ1c2VUaGVtZSIsIlRvYXN0ZXIiLCJTb25uZXIiLCJwcm9wcyIsInRoZW1lIiwiY2xhc3NOYW1lIiwic3R5bGUiXSwiaWdub3JlTGlzdCI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/ui/sonner.tsx\n"));

/***/ })

},
/******/ __webpack_require__ => { // webpackRuntimeModules
/******/ var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
/******/ __webpack_require__.O(0, ["main-app"], () => (__webpack_exec__("(app-pages-browser)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CPC%5C%5CDownloads%5C%5CNew%20folder%20(22)%5C%5Cviral-clip-generator%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%5C%5C%5C%5Capp%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Inter%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22inter%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CPC%5C%5CDownloads%5C%5CNew%20folder%20(22)%5C%5Cviral-clip-generator%5C%5Csrc%5C%5Capp%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CPC%5C%5CDownloads%5C%5CNew%20folder%20(22)%5C%5Cviral-clip-generator%5C%5Csrc%5C%5Ccomponents%5C%5Ctheme-provider.tsx%22%2C%22ids%22%3A%5B%22ThemeProvider%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CPC%5C%5CDownloads%5C%5CNew%20folder%20(22)%5C%5Cviral-clip-generator%5C%5Csrc%5C%5Ccomponents%5C%5Cui%5C%5Csonner.tsx%22%2C%22ids%22%3A%5B%22Toaster%22%5D%7D&server=false!")));
/******/ var __webpack_exports__ = __webpack_require__.O();
/******/ _N_E = __webpack_exports__;
/******/ }
]);